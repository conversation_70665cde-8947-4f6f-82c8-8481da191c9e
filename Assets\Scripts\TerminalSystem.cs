using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System;
using System.Linq;

public class TerminalSystem : MonoBehaviour
{
    [Header("UI References")]
    public TMP_InputField commandInput;
    public TMP_Text outputText;
    public ScrollRect scrollRect;
    public GameObject terminalWindow;
    
    [Header("Terminal Settings")]
    public string promptPrefix = "user@system:~$ ";
    public Color normalTextColor = Color.white;
    public Color errorTextColor = Color.red;
    public Color successTextColor = Color.green;
    public Color warningTextColor = Color.yellow;
    
    private List<string> commandHistory = new List<string>();
    private int historyIndex = -1;
    private Dictionary<string, System.Action<string[]>> commands;
    private SystemSimulator systemSimulator;
    
    void Start()
    {
        InitializeCommands();
        systemSimulator = FindObjectOfType<SystemSimulator>();
        
        if (commandInput != null)
        {
            commandInput.onEndEdit.AddListener(ProcessCommand);
            commandInput.Select();
            commandInput.ActivateInputField();
        }
        
        AddOutput("Terminal System Initialized", successTextColor);
        AddOutput("Type 'help' for available commands", normalTextColor);
        ShowPrompt();
    }
    
    void Update()
    {
        HandleKeyboardInput();
    }
    
    void HandleKeyboardInput()
    {
        if (Input.GetKeyDown(KeyCode.UpArrow))
        {
            NavigateHistory(-1);
        }
        else if (Input.GetKeyDown(KeyCode.DownArrow))
        {
            NavigateHistory(1);
        }
        else if (Input.GetKeyDown(KeyCode.Tab))
        {
            AutoComplete();
        }
    }
    
    void InitializeCommands()
    {
        commands = new Dictionary<string, System.Action<string[]>>
        {
            {"help", ShowHelp},
            {"clear", ClearTerminal},
            {"ls", ListFiles},
            {"dir", ListFiles},
            {"pwd", ShowCurrentDirectory},
            {"cd", ChangeDirectory},
            {"mkdir", CreateDirectory},
            {"touch", CreateFile},
            {"cat", ShowFileContent},
            {"echo", EchoText},
            {"ps", ShowProcesses},
            {"kill", KillProcess},
            {"top", ShowSystemInfo},
            {"ping", PingHost},
            {"wget", DownloadFile},
            {"nano", EditFile},
            {"chmod", ChangePermissions},
            {"sudo", ExecuteAsRoot},
            {"systemctl", SystemControl},
            {"service", ServiceControl},
            {"netstat", ShowNetworkStatus},
            {"ifconfig", ShowNetworkConfig},
            {"history", ShowHistory},
            {"exit", ExitTerminal}
        };
    }
    
    void ProcessCommand(string input)
    {
        if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
        {
            string command = commandInput.text.Trim();
            commandInput.text = "";
            
            if (!string.IsNullOrEmpty(command))
            {
                AddOutput(promptPrefix + command, normalTextColor);
                commandHistory.Add(command);
                historyIndex = commandHistory.Count;
                
                ExecuteCommand(command);
            }
            
            ShowPrompt();
            commandInput.Select();
            commandInput.ActivateInputField();
        }
    }
    
    void ExecuteCommand(string commandLine)
    {
        string[] parts = commandLine.Split(' ');
        string command = parts[0].ToLower();
        string[] args = parts.Skip(1).ToArray();
        
        if (commands.ContainsKey(command))
        {
            try
            {
                commands[command](args);
            }
            catch (Exception e)
            {
                AddOutput($"Error executing command: {e.Message}", errorTextColor);
            }
        }
        else
        {
            AddOutput($"Command not found: {command}", errorTextColor);
            AddOutput("Type 'help' for available commands", warningTextColor);
        }
    }
    
    void ShowHelp(string[] args)
    {
        AddOutput("Available Commands:", successTextColor);
        AddOutput("help          - Show this help message", normalTextColor);
        AddOutput("clear         - Clear terminal screen", normalTextColor);
        AddOutput("ls/dir        - List directory contents", normalTextColor);
        AddOutput("pwd           - Show current directory", normalTextColor);
        AddOutput("cd <path>     - Change directory", normalTextColor);
        AddOutput("mkdir <name>  - Create directory", normalTextColor);
        AddOutput("touch <file>  - Create file", normalTextColor);
        AddOutput("cat <file>    - Show file content", normalTextColor);
        AddOutput("echo <text>   - Display text", normalTextColor);
        AddOutput("ps            - Show running processes", normalTextColor);
        AddOutput("kill <pid>    - Kill process", normalTextColor);
        AddOutput("top           - Show system information", normalTextColor);
        AddOutput("ping <host>   - Ping network host", normalTextColor);
        AddOutput("history       - Show command history", normalTextColor);
        AddOutput("exit          - Close terminal", normalTextColor);
    }
    
    void ClearTerminal(string[] args)
    {
        outputText.text = "";
    }
    
    void ListFiles(string[] args)
    {
        if (systemSimulator != null)
        {
            var files = systemSimulator.GetCurrentDirectoryContents();
            foreach (var file in files)
            {
                string permissions = file.IsDirectory ? "drwxr-xr-x" : "-rw-r--r--";
                string size = file.IsDirectory ? "4096" : file.Size.ToString();
                string date = file.LastModified.ToString("MMM dd HH:mm");
                
                AddOutput($"{permissions} 1 user user {size,8} {date} {file.Name}", normalTextColor);
            }
        }
        else
        {
            AddOutput("total 0", normalTextColor);
            AddOutput("drwxr-xr-x 2 <USER> <GROUP> 4096 Dec 15 10:30 Documents", normalTextColor);
            AddOutput("drwxr-xr-x 2 <USER> <GROUP> 4096 Dec 15 10:30 Downloads", normalTextColor);
            AddOutput("-rw-r--r-- 1 <USER> <GROUP> 1024 Dec 15 10:30 readme.txt", normalTextColor);
        }
    }
    
    void ShowCurrentDirectory(string[] args)
    {
        if (systemSimulator != null)
        {
            AddOutput(systemSimulator.GetCurrentDirectory(), normalTextColor);
        }
        else
        {
            AddOutput("/home/<USER>", normalTextColor);
        }
    }
    
    void ChangeDirectory(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("/home/<USER>", normalTextColor);
            return;
        }
        
        string path = args[0];
        if (systemSimulator != null)
        {
            if (systemSimulator.ChangeDirectory(path))
            {
                // Directory changed successfully
            }
            else
            {
                AddOutput($"cd: {path}: No such file or directory", errorTextColor);
            }
        }
        else
        {
            AddOutput($"Changed directory to: {path}", normalTextColor);
        }
    }
    
    void CreateDirectory(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("mkdir: missing operand", errorTextColor);
            return;
        }
        
        string dirName = args[0];
        if (systemSimulator != null)
        {
            if (systemSimulator.CreateDirectory(dirName))
            {
                AddOutput($"Directory '{dirName}' created", successTextColor);
            }
            else
            {
                AddOutput($"mkdir: cannot create directory '{dirName}': File exists", errorTextColor);
            }
        }
        else
        {
            AddOutput($"Directory '{dirName}' created", successTextColor);
        }
    }
    
    void CreateFile(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("touch: missing file operand", errorTextColor);
            return;
        }
        
        string fileName = args[0];
        if (systemSimulator != null)
        {
            systemSimulator.CreateFile(fileName, "");
            AddOutput($"File '{fileName}' created", successTextColor);
        }
        else
        {
            AddOutput($"File '{fileName}' created", successTextColor);
        }
    }
    
    void ShowFileContent(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("cat: missing file operand", errorTextColor);
            return;
        }
        
        string fileName = args[0];
        if (systemSimulator != null)
        {
            string content = systemSimulator.GetFileContent(fileName);
            if (content != null)
            {
                AddOutput(content, normalTextColor);
            }
            else
            {
                AddOutput($"cat: {fileName}: No such file or directory", errorTextColor);
            }
        }
        else
        {
            AddOutput($"Content of {fileName}:", normalTextColor);
            AddOutput("This is a sample file content.", normalTextColor);
        }
    }
    
    void EchoText(string[] args)
    {
        string text = string.Join(" ", args);
        AddOutput(text, normalTextColor);
    }
    
    void ShowProcesses(string[] args)
    {
        AddOutput("  PID TTY          TIME CMD", normalTextColor);
        AddOutput(" 1234 pts/0    00:00:01 bash", normalTextColor);
        AddOutput(" 1235 pts/0    00:00:00 unity", normalTextColor);
        AddOutput(" 1236 pts/0    00:00:00 terminal", normalTextColor);
        AddOutput(" 1237 pts/0    00:00:00 ps", normalTextColor);
    }
    
    void KillProcess(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("kill: usage: kill [-s sigspec | -n signum | -sigspec] pid | jobspec ... or kill -l [sigspec]", errorTextColor);
            return;
        }
        
        string pid = args[0];
        AddOutput($"Process {pid} terminated", successTextColor);
    }
    
    void ShowSystemInfo(string[] args)
    {
        AddOutput("System Information:", successTextColor);
        AddOutput($"CPU Usage: {UnityEngine.Random.Range(10, 80)}%", normalTextColor);
        AddOutput($"Memory Usage: {UnityEngine.Random.Range(40, 90)}%", normalTextColor);
        AddOutput($"Disk Usage: {UnityEngine.Random.Range(20, 70)}%", normalTextColor);
        AddOutput($"Network: {UnityEngine.Random.Range(1, 100)} KB/s", normalTextColor);
    }
    
    void PingHost(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("ping: usage: ping <hostname>", errorTextColor);
            return;
        }
        
        string host = args[0];
        StartCoroutine(SimulatePing(host));
    }
    
    IEnumerator SimulatePing(string host)
    {
        AddOutput($"PING {host} (***********): 56 data bytes", normalTextColor);
        
        for (int i = 0; i < 4; i++)
        {
            yield return new WaitForSeconds(1f);
            int time = UnityEngine.Random.Range(10, 100);
            AddOutput($"64 bytes from {host}: icmp_seq={i + 1} ttl=64 time={time}ms", normalTextColor);
        }
        
        AddOutput($"--- {host} ping statistics ---", normalTextColor);
        AddOutput("4 packets transmitted, 4 received, 0% packet loss", successTextColor);
    }
    
    void DownloadFile(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("wget: missing URL", errorTextColor);
            return;
        }
        
        string url = args[0];
        StartCoroutine(SimulateDownload(url));
    }
    
    IEnumerator SimulateDownload(string url)
    {
        AddOutput($"--{DateTime.Now:yyyy-MM-dd HH:mm:ss}--  {url}", normalTextColor);
        AddOutput("Resolving hostname... done.", normalTextColor);
        AddOutput("Connecting to server... connected.", normalTextColor);
        
        yield return new WaitForSeconds(0.5f);
        
        for (int i = 0; i <= 100; i += 10)
        {
            AddOutput($"Downloading... {i}%", normalTextColor);
            yield return new WaitForSeconds(0.2f);
        }
        
        AddOutput("Download completed successfully!", successTextColor);
    }
    
    void EditFile(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("nano: missing filename", errorTextColor);
            return;
        }
        
        string fileName = args[0];
        AddOutput($"Opening {fileName} in nano editor...", normalTextColor);
        AddOutput("(This is a simulation - file editing not implemented)", warningTextColor);
    }
    
    void ChangePermissions(string[] args)
    {
        if (args.Length < 2)
        {
            AddOutput("chmod: missing operand", errorTextColor);
            return;
        }
        
        string permissions = args[0];
        string fileName = args[1];
        AddOutput($"Changed permissions of '{fileName}' to {permissions}", successTextColor);
    }
    
    void ExecuteAsRoot(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("sudo: a command is required", errorTextColor);
            return;
        }
        
        AddOutput("[sudo] password for user: ", warningTextColor);
        yield return new WaitForSeconds(1f);
        
        string command = string.Join(" ", args);
        AddOutput($"Executing as root: {command}", successTextColor);
        ExecuteCommand(command);
    }
    
    void SystemControl(string[] args)
    {
        if (args.Length == 0)
        {
            AddOutput("systemctl: missing command", errorTextColor);
            return;
        }
        
        string action = args[0];
        string service = args.Length > 1 ? args[1] : "unknown";
        
        switch (action.ToLower())
        {
            case "status":
                AddOutput($"● {service}.service - {service} Service", successTextColor);
                AddOutput("   Loaded: loaded (/etc/systemd/system/service.service; enabled)", normalTextColor);
                AddOutput("   Active: active (running)", successTextColor);
                break;
            case "start":
                AddOutput($"Started {service} service", successTextColor);
                break;
            case "stop":
                AddOutput($"Stopped {service} service", successTextColor);
                break;
            case "restart":
                AddOutput($"Restarted {service} service", successTextColor);
                break;
            default:
                AddOutput($"Unknown systemctl command: {action}", errorTextColor);
                break;
        }
    }
    
    void ServiceControl(string[] args)
    {
        if (args.Length < 2)
        {
            AddOutput("service: usage: service <service> <action>", errorTextColor);
            return;
        }
        
        string serviceName = args[0];
        string action = args[1];
        AddOutput($"{serviceName}: {action}", successTextColor);
    }
    
    void ShowNetworkStatus(string[] args)
    {
        AddOutput("Active Internet connections (w/o servers)", normalTextColor);
        AddOutput("Proto Recv-Q Send-Q Local Address           Foreign Address         State", normalTextColor);
        AddOutput("tcp        0      0 ***********00:22       ***********:54321       ESTABLISHED", normalTextColor);
        AddOutput("tcp        0      0 ***********00:80       0.0.0.0:*               LISTEN", normalTextColor);
    }
    
    void ShowNetworkConfig(string[] args)
    {
        AddOutput("eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500", normalTextColor);
        AddOutput("        inet ***********00  netmask *************  broadcast *************", normalTextColor);
        AddOutput("        inet6 fe80::a00:27ff:fe4e:66a1  prefixlen 64  scopeid 0x20<link>", normalTextColor);
        AddOutput("        ether 08:00:27:4e:66:a1  txqueuelen 1000  (Ethernet)", normalTextColor);
    }
    
    void ShowHistory(string[] args)
    {
        for (int i = 0; i < commandHistory.Count; i++)
        {
            AddOutput($"{i + 1,4}  {commandHistory[i]}", normalTextColor);
        }
    }
    
    void ExitTerminal(string[] args)
    {
        AddOutput("Goodbye!", successTextColor);
        if (terminalWindow != null)
        {
            terminalWindow.SetActive(false);
        }
    }
    
    void NavigateHistory(int direction)
    {
        if (commandHistory.Count == 0) return;
        
        historyIndex += direction;
        historyIndex = Mathf.Clamp(historyIndex, 0, commandHistory.Count);
        
        if (historyIndex < commandHistory.Count)
        {
            commandInput.text = commandHistory[historyIndex];
            commandInput.caretPosition = commandInput.text.Length;
        }
        else
        {
            commandInput.text = "";
        }
    }
    
    void AutoComplete()
    {
        string currentText = commandInput.text;
        var matches = commands.Keys.Where(cmd => cmd.StartsWith(currentText)).ToList();
        
        if (matches.Count == 1)
        {
            commandInput.text = matches[0];
            commandInput.caretPosition = commandInput.text.Length;
        }
        else if (matches.Count > 1)
        {
            AddOutput("Available completions:", warningTextColor);
            foreach (var match in matches)
            {
                AddOutput(match, normalTextColor);
            }
            ShowPrompt();
        }
    }
    
    void ShowPrompt()
    {
        // This could be enhanced to show current directory in prompt
    }
    
    void AddOutput(string text, Color color)
    {
        if (outputText != null)
        {
            string colorHex = ColorUtility.ToHtmlStringRGB(color);
            outputText.text += $"<color=#{colorHex}>{text}</color>\n";
            
            // Auto-scroll to bottom
            Canvas.ForceUpdateCanvases();
            if (scrollRect != null)
            {
                scrollRect.verticalNormalizedPosition = 0f;
            }
        }
    }
    
    public void ToggleTerminal()
    {
        if (terminalWindow != null)
        {
            terminalWindow.SetActive(!terminalWindow.activeInHierarchy);
            if (terminalWindow.activeInHierarchy)
            {
                commandInput.Select();
                commandInput.ActivateInputField();
            }
        }
    }
}
