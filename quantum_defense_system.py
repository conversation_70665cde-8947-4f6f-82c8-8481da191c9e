#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUTONOMOUS COUNTER-DRONE DEFENSE SYSTEM
Quantum Mechanics Based Turret System - PARTIZAN
Real-time autonomous drone detection and engagement
"""

import os
import sys
import time
import random
import threading
import math
from datetime import datetime
from collections import defaultdict

class QuantumTurret:
    def __init__(self):
        self.quantum_radar_active = True
        self.quantum_camera_active = True
        self.quantum_processor_load = 0
        self.quantum_battery_level = 100
        self.quantum_communication_status = "ONLINE"
        self.turret_rotation = 0  # degrees
        self.turret_elevation = 0  # degrees
        self.targeting_accuracy = 98.5  # percentage
        self.engagement_range = 5000  # meters
        self.ammunition_count = 500
        self.system_temperature = 25  # celsius
        self.quantum_coherence = 99.8  # percentage
        
    def rotate_turret(self, target_bearing):
        """Rotate turret to target bearing"""
        rotation_speed = 45  # degrees per second
        angle_diff = (target_bearing - self.turret_rotation + 180) % 360 - 180
        
        if abs(angle_diff) > 1:
            if angle_diff > 0:
                self.turret_rotation += min(rotation_speed, angle_diff)
            else:
                self.turret_rotation -= min(rotation_speed, abs(angle_diff))
            self.turret_rotation = self.turret_rotation % 360
            return False  # Still rotating
        return True  # On target
    
    def elevate_turret(self, target_elevation):
        """Elevate turret to target elevation"""
        elevation_speed = 30  # degrees per second
        angle_diff = target_elevation - self.turret_elevation
        
        if abs(angle_diff) > 0.5:
            if angle_diff > 0:
                self.turret_elevation += min(elevation_speed, angle_diff)
            else:
                self.turret_elevation -= min(elevation_speed, abs(angle_diff))
            self.turret_elevation = max(-10, min(90, self.turret_elevation))
            return False  # Still moving
        return True  # On target

class Drone:
    def __init__(self, drone_id, x, y, altitude, speed, heading):
        self.id = drone_id
        self.x = x
        self.y = y
        self.altitude = altitude
        self.speed = speed  # m/s
        self.heading = heading  # degrees
        self.detected = False
        self.tracked = False
        self.classified = False
        self.threat_level = random.choice(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
        self.size = random.choice(['MICRO', 'SMALL', 'MEDIUM', 'LARGE'])
        self.type = random.choice(['CIVILIAN', 'COMMERCIAL', 'MILITARY', 'HOSTILE'])
        self.signature = random.uniform(0.1, 2.0)  # RCS in m²
        self.last_seen = datetime.now()
        self.engaged = False
        self.destroyed = False
        self.evasive_maneuvers = False
        
    def update_position(self, dt):
        if not self.destroyed:
            # Evasive maneuvers if engaged
            if self.engaged and not self.evasive_maneuvers:
                self.evasive_maneuvers = True
                self.speed *= 1.5  # Increase speed
                
            if self.evasive_maneuvers:
                # Random evasive movements
                self.heading += random.uniform(-30, 30)
                self.altitude += random.uniform(-50, 50)
            else:
                # Normal flight pattern
                self.heading += random.uniform(-5, 5)
                self.altitude += random.uniform(-10, 10)
            
            # Update position
            heading_rad = math.radians(self.heading)
            distance = self.speed * dt
            
            self.x += distance * math.sin(heading_rad)
            self.y += distance * math.cos(heading_rad)
            self.altitude = max(10, min(3000, self.altitude))
            
            self.last_seen = datetime.now()

class QuantumDefenseSystem:
    def __init__(self):
        self.turret = QuantumTurret()
        self.drones = {}
        self.system_status = "OPERATIONAL"
        self.threat_level = "GREEN"
        self.auto_engage = True
        self.engagement_log = []
        self.base_x = 0
        self.base_y = 0
        self.current_target = None
        self.quantum_field_strength = 100
        
        # Performance metrics
        self.targets_engaged = 0
        self.targets_destroyed = 0
        self.shots_fired = 0
        self.accuracy_rate = 0
        
        # Start subsystems
        self.start_quantum_radar()
        self.start_drone_generator()
        self.start_tracking_system()
        self.start_quantum_processor()
        
    def print_colored(self, text, color="white", end="\n"):
        colors = {
            "red": "\033[91m", "green": "\033[92m", "yellow": "\033[93m",
            "blue": "\033[94m", "magenta": "\033[95m", "cyan": "\033[96m",
            "white": "\033[97m", "reset": "\033[0m", "bold": "\033[1m"
        }
        print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}", end=end)
    
    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def calculate_distance(self, x1, y1, x2, y2):
        return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
    
    def calculate_3d_distance(self, x1, y1, z1, x2, y2, z2):
        return math.sqrt((x2 - x1)**2 + (y2 - y1)**2 + (z2 - z1)**2)
    
    def calculate_bearing(self, x1, y1, x2, y2):
        angle = math.degrees(math.atan2(x2 - x1, y2 - y1))
        return (angle + 360) % 360
    
    def calculate_elevation(self, distance, altitude):
        return math.degrees(math.atan2(altitude, distance))
    
    def start_quantum_radar(self):
        def quantum_radar_sweep():
            while True:
                if self.turret.quantum_radar_active:
                    # Quantum radar has enhanced detection capabilities
                    for drone in self.drones.values():
                        distance = self.calculate_distance(
                            self.base_x, self.base_y, drone.x, drone.y
                        )
                        
                        if distance <= self.turret.engagement_range and not drone.destroyed:
                            # Quantum detection probability
                            detection_prob = 0.99  # Very high due to quantum mechanics
                            if drone.size == "MICRO":
                                detection_prob = 0.85
                            
                            if random.random() < detection_prob:
                                if not drone.detected:
                                    drone.detected = True
                                    self.log_event(f"QUANTUM RADAR: Contact detected - ID:{drone.id}")
                                
                                drone.tracked = True
                                
                                # Quantum classification
                                if not drone.classified:
                                    drone.classified = True
                                    self.log_event(f"QUANTUM AI: Target classified - {drone.type}")
                        else:
                            drone.detected = False
                            drone.tracked = False
                
                # Update quantum processor load
                active_tracks = sum(1 for d in self.drones.values() if d.tracked)
                self.turret.quantum_processor_load = min(100, active_tracks * 15)
                
                time.sleep(0.5)  # High-speed quantum scanning
        
        thread = threading.Thread(target=quantum_radar_sweep, daemon=True)
        thread.start()
    
    def start_drone_generator(self):
        def generate_drones():
            drone_id = 1
            while True:
                time.sleep(random.uniform(8, 20))
                
                # Generate drone at random position
                angle = random.uniform(0, 360)
                distance = random.uniform(3000, 6000)
                
                x = self.base_x + distance * math.sin(math.radians(angle))
                y = self.base_y + distance * math.cos(math.radians(angle))
                
                altitude = random.uniform(50, 1500)
                speed = random.uniform(10, 80)  # m/s
                heading = random.uniform(0, 360)
                
                drone = Drone(drone_id, x, y, altitude, speed, heading)
                self.drones[drone_id] = drone
                
                drone_id += 1
                self.cleanup_drones()
        
        thread = threading.Thread(target=generate_drones, daemon=True)
        thread.start()
    
    def start_tracking_system(self):
        def update_tracking():
            while True:
                for drone in list(self.drones.values()):
                    if not drone.destroyed:
                        drone.update_position(0.1)
                        
                        distance = self.calculate_distance(
                            self.base_x, self.base_y, drone.x, drone.y
                        )
                        
                        if distance > 8000:
                            del self.drones[drone.id]
                            continue
                        
                        # Auto-engage hostile targets
                        if (self.auto_engage and drone.tracked and 
                            drone.type in ["MILITARY", "HOSTILE"] and 
                            not drone.engaged and distance <= self.turret.engagement_range):
                            self.engage_target(drone.id)
                
                self.update_threat_level()
                self.update_quantum_systems()
                time.sleep(0.1)
        
        thread = threading.Thread(target=update_tracking, daemon=True)
        thread.start()
    
    def start_quantum_processor(self):
        def quantum_processing():
            while True:
                # Simulate quantum processing effects
                self.turret.quantum_coherence = max(95, 
                    self.turret.quantum_coherence + random.uniform(-0.5, 0.1))
                
                # Battery consumption
                consumption = 0.1 + (self.turret.quantum_processor_load / 1000)
                self.turret.quantum_battery_level = max(0, 
                    self.turret.quantum_battery_level - consumption)
                
                # Temperature management
                heat_generation = self.turret.quantum_processor_load / 10
                self.turret.system_temperature = 25 + heat_generation + random.uniform(-2, 2)
                
                time.sleep(1)
        
        thread = threading.Thread(target=quantum_processing, daemon=True)
        thread.start()
    
    def cleanup_drones(self):
        current_time = datetime.now()
        to_remove = []
        
        for drone_id, drone in self.drones.items():
            if drone.destroyed:
                time_since_destruction = (current_time - drone.last_seen).seconds
                if time_since_destruction > 10:
                    to_remove.append(drone_id)
        
        for drone_id in to_remove:
            del self.drones[drone_id]
    
    def update_threat_level(self):
        hostile_count = sum(1 for d in self.drones.values() 
                          if d.type in ["MILITARY", "HOSTILE"] and d.tracked and not d.destroyed)
        
        if hostile_count == 0:
            self.threat_level = "GREEN"
        elif hostile_count <= 1:
            self.threat_level = "YELLOW"
        elif hostile_count <= 3:
            self.threat_level = "ORANGE"
        else:
            self.threat_level = "RED"
    
    def update_quantum_systems(self):
        # Update quantum field strength based on system load
        load_factor = self.turret.quantum_processor_load / 100
        self.quantum_field_strength = max(80, 100 - (load_factor * 20))
    
    def log_event(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        self.engagement_log.append(f"[{timestamp}] {message}")
        if len(self.engagement_log) > 20:
            self.engagement_log.pop(0)
    
    def engage_target(self, drone_id):
        if drone_id not in self.drones:
            return False, "Target not found"
        
        drone = self.drones[drone_id]
        
        if drone.destroyed:
            return False, "Target already destroyed"
        
        if self.turret.ammunition_count <= 0:
            return False, "No ammunition available"
        
        distance = self.calculate_distance(self.base_x, self.base_y, drone.x, drone.y)
        
        if distance > self.turret.engagement_range:
            return False, f"Target out of range"
        
        # Calculate targeting solution
        bearing = self.calculate_bearing(self.base_x, self.base_y, drone.x, drone.y)
        elevation = self.calculate_elevation(distance, drone.altitude)
        
        drone.engaged = True
        self.current_target = drone_id
        self.targets_engaged += 1
        
        self.log_event(f"TARGETING: Acquiring target ID:{drone_id}")
        
        # Simulate turret movement and engagement
        def engagement_sequence():
            # Turret alignment phase
            alignment_time = 0
            while alignment_time < 3:  # Max 3 seconds to align
                bearing_aligned = self.turret.rotate_turret(bearing)
                elevation_aligned = self.turret.elevate_turret(elevation)
                
                if bearing_aligned and elevation_aligned:
                    break
                
                time.sleep(0.1)
                alignment_time += 0.1
            
            self.log_event(f"TURRET: Aligned on target ID:{drone_id}")
            
            # Quantum targeting calculation
            time.sleep(0.5)
            
            # Fire quantum-enhanced projectile
            self.turret.ammunition_count -= 1
            self.shots_fired += 1
            
            self.log_event(f"QUANTUM TURRET: Firing at target ID:{drone_id}")
            
            # Calculate hit probability with quantum enhancement
            base_accuracy = self.turret.targeting_accuracy / 100
            quantum_bonus = self.turret.quantum_coherence / 100 * 0.1
            range_factor = max(0.7, 1 - (distance / self.turret.engagement_range) * 0.3)
            evasion_penalty = 0.8 if drone.evasive_maneuvers else 1.0
            
            hit_probability = base_accuracy * (1 + quantum_bonus) * range_factor * evasion_penalty
            
            # Projectile flight time
            projectile_speed = 1200  # m/s
            flight_time = distance / projectile_speed
            time.sleep(min(flight_time, 2))
            
            if random.random() < hit_probability:
                drone.destroyed = True
                self.targets_destroyed += 1
                self.log_event(f"TARGET DESTROYED: ID:{drone_id} eliminated")
                self.print_colored(f"🎯 TARGET DESTROYED: Drone {drone_id}", "green")
            else:
                drone.engaged = False
                self.log_event(f"TARGET MISSED: ID:{drone_id} evaded")
                self.print_colored(f"❌ MISS: Drone {drone_id} evaded", "yellow")
            
            self.current_target = None
            
            # Update accuracy rate
            if self.shots_fired > 0:
                self.accuracy_rate = (self.targets_destroyed / self.shots_fired) * 100
        
        thread = threading.Thread(target=engagement_sequence, daemon=True)
        thread.start()
        
        return True, f"Engaging target ID:{drone_id}"
    
    def display_system_status(self):
        self.clear_screen()
        
        # Header
        self.print_colored("=" * 90, "cyan")
        self.print_colored("    AUTONOMOUS COUNTER-DRONE DEFENSE SYSTEM - PARTIZAN", "bold")
        self.print_colored("         QUANTUM MECHANICS TURRET SYSTEM v3.0", "cyan")
        self.print_colored("=" * 90, "cyan")
        
        # System status row 1
        print("┌─ QUANTUM SYSTEMS ─────────────────┬─ TURRET STATUS ──────────────────┬─ ENGAGEMENT ─────────┐")
        
        # Quantum systems
        radar_status = "ACTIVE" if self.turret.quantum_radar_active else "OFFLINE"
        radar_color = "green" if self.turret.quantum_radar_active else "red"
        
        camera_status = "ACTIVE" if self.turret.quantum_camera_active else "OFFLINE"
        camera_color = "green" if self.turret.quantum_camera_active else "red"
        
        comm_color = "green" if self.turret.quantum_communication_status == "ONLINE" else "red"
        
        print("│ Quantum Radar:     ", end="")
        self.print_colored(f"{radar_status:<12}", radar_color, end="")
        print("│ Rotation:    ", end="")
        self.print_colored(f"{self.turret.turret_rotation:6.1f}°", "cyan", end="")
        print("      │ Targets Engaged: ", end="")
        self.print_colored(f"{self.targets_engaged:4d}", "yellow", end="")
        print(" │")
        
        print("│ Quantum Camera:    ", end="")
        self.print_colored(f"{camera_status:<12}", camera_color, end="")
        print("│ Elevation:   ", end="")
        self.print_colored(f"{self.turret.turret_elevation:6.1f}°", "cyan", end="")
        print("      │ Targets Destroyed:", end="")
        self.print_colored(f"{self.targets_destroyed:4d}", "green", end="")
        print(" │")
        
        print("│ Quantum Processor: ", end="")
        self.print_colored(f"{self.turret.quantum_processor_load:3.0f}%", "yellow", end="")
        print("        │ Ammunition:  ", end="")
        ammo_color = "red" if self.turret.ammunition_count < 50 else "green"
        self.print_colored(f"{self.turret.ammunition_count:6d}", ammo_color, end="")
        print("      │ Accuracy Rate:   ", end="")
        acc_color = "green" if self.accuracy_rate > 80 else "yellow" if self.accuracy_rate > 60 else "red"
        self.print_colored(f"{self.accuracy_rate:5.1f}%", acc_color, end="")
        print("│")
        
        print("│ Quantum Battery:   ", end="")
        battery_color = "red" if self.turret.quantum_battery_level < 20 else "yellow" if self.turret.quantum_battery_level < 50 else "green"
        self.print_colored(f"{self.turret.quantum_battery_level:5.1f}%", battery_color, end="")
        print("       │ Temperature: ", end="")
        temp_color = "red" if self.turret.system_temperature > 60 else "yellow" if self.turret.system_temperature > 45 else "green"
        self.print_colored(f"{self.turret.system_temperature:5.1f}°C", temp_color, end="")
        print("      │ Shots Fired:     ", end="")
        self.print_colored(f"{self.shots_fired:4d}", "cyan", end="")
        print(" │")
        
        print("│ Quantum Communication:", end="")
        self.print_colored(f"{self.turret.quantum_communication_status:<8}", comm_color, end="")
        print("│ Range:       ", end="")
        self.print_colored(f"{self.turret.engagement_range:6d}m", "cyan", end="")
        print("     │ Current Target:  ", end="")
        target_display = f"ID:{self.current_target}" if self.current_target else "NONE"
        target_color = "red" if self.current_target else "green"
        self.print_colored(f"{target_display:<4}", target_color, end="")
        print(" │")
        
        print("└───────────────────────────────────┴──────────────────────────────────┴──────────────────────┘")
        
        # Threat level and system status
        threat_colors = {"GREEN": "green", "YELLOW": "yellow", "ORANGE": "yellow", "RED": "red"}
        print(f"\nThreat Level: ", end="")
        self.print_colored(f"{self.threat_level}", threat_colors.get(self.threat_level, "white"), end="")
        print(f" | System Status: ", end="")
        self.print_colored(f"{self.system_status}", "green", end="")
        print(f" | Auto-Engage: ", end="")
        self.print_colored("ENABLED" if self.auto_engage else "DISABLED", "green" if self.auto_engage else "red")
        
        # Tracked contacts
        tracked_drones = [d for d in self.drones.values() if d.tracked and not d.destroyed]
        
        self.print_colored("\n┌─ TRACKED CONTACTS ─────────────────────────────────────────────────────────────────┐", "blue")
        
        if tracked_drones:
            print("│ ID │ TYPE       │ THREAT   │ DISTANCE │ ALTITUDE │ SPEED │ STATUS     │ SIGNATURE │")
            print("├────┼────────────┼──────────┼──────────┼──────────┼───────┼────────────┼───────────┤")
            
            for drone in sorted(tracked_drones, key=lambda d: d.id)[:8]:  # Show max 8
                distance = self.calculate_distance(self.base_x, self.base_y, drone.x, drone.y)
                
                status = "🎯ENGAGED" if drone.engaged else "👁 TRACKING"
                status_color = "red" if drone.engaged else "green"
                
                threat_color = {"LOW": "green", "MEDIUM": "yellow", 
                              "HIGH": "yellow", "CRITICAL": "red"}.get(drone.threat_level, "white")
                
                type_color = {"HOSTILE": "red", "MILITARY": "yellow", 
                            "COMMERCIAL": "cyan", "CIVILIAN": "green"}.get(drone.type, "white")
                
                print(f"│{drone.id:3d} │ ", end="")
                self.print_colored(f"{drone.type:<10}", type_color, end="")
                print(" │ ", end="")
                self.print_colored(f"{drone.threat_level:<8}", threat_color, end="")
                print(f" │ {distance:7.0f}m │ {drone.altitude:7.0f}m │ {drone.speed:4.0f}m/s │ ", end="")
                self.print_colored(f"{status:<10}", status_color, end="")
                print(f" │ {drone.signature:7.2f}m² │")
        else:
            print("│                              NO CONTACTS TRACKED                              │")
        
        print("└────────────────────────────────────────────────────────────────────────────────┘")
        
        # System log
        self.print_colored("\n┌─ SYSTEM LOG ───────────────────────────────────────────────────────────────────────┐", "cyan")
        for event in self.engagement_log[-6:]:  # Show last 6 events
            if "DESTROYED" in event:
                print("│ ", end="")
                self.print_colored(event[:78], "green", end="")
                print(" │")
            elif "MISSED" in event or "EVADED" in event:
                print("│ ", end="")
                self.print_colored(event[:78], "yellow", end="")
                print(" │")
            elif "FIRING" in event or "ENGAGING" in event:
                print("│ ", end="")
                self.print_colored(event[:78], "red", end="")
                print(" │")
            else:
                print(f"│ {event[:78]:<78} │")
        
        # Fill empty log lines
        for _ in range(6 - len(self.engagement_log[-6:])):
            print("│" + " " * 78 + "│")
        
        print("└────────────────────────────────────────────────────────────────────────────────┘")
        
        # Commands
        self.print_colored("\nCommands: [E]ngage [A]uto-toggle [R]adar [S]tatus [Q]uit", "yellow")
    
    def run_system(self):
        while True:
            try:
                self.display_system_status()

                # Auto-refresh every 2 seconds
                time.sleep(2)

            except KeyboardInterrupt:
                self.print_colored("\n🔴 EMERGENCY SHUTDOWN", "red")
                break
            except:
                continue
    
    def show_detailed_status(self):
        self.clear_screen()
        self.print_colored("DETAILED QUANTUM SYSTEM ANALYSIS", "cyan")
        self.print_colored("=" * 60, "cyan")
        
        print(f"🔬 Quantum Coherence Level: {self.turret.quantum_coherence:.2f}%")
        print(f"⚡ Quantum Field Strength: {self.quantum_field_strength:.1f}%")
        print(f"🌡️  System Temperature: {self.turret.system_temperature:.1f}°C")
        print(f"🔋 Power Consumption: {self.turret.quantum_processor_load/10:.1f}kW")
        print(f"📊 Processing Load: {self.turret.quantum_processor_load:.0f}%")
        print(f"🎯 Targeting Accuracy: {self.turret.targeting_accuracy:.1f}%")
        print(f"📡 Detection Range: {self.turret.engagement_range}m")
        print(f"💥 Ammunition Remaining: {self.turret.ammunition_count}")
        
        print(f"\n📈 PERFORMANCE METRICS:")
        print(f"   Targets Engaged: {self.targets_engaged}")
        print(f"   Targets Destroyed: {self.targets_destroyed}")
        print(f"   Shots Fired: {self.shots_fired}")
        print(f"   Hit Rate: {self.accuracy_rate:.1f}%")
        
        efficiency = (self.targets_destroyed / max(1, self.targets_engaged)) * 100
        print(f"   System Efficiency: {efficiency:.1f}%")

def main():
    print("🚀 Initializing Quantum Defense System...")
    time.sleep(1)
    
    defense_system = QuantumDefenseSystem()
    
    # Startup sequence
    defense_system.clear_screen()
    defense_system.print_colored("QUANTUM DEFENSE SYSTEM INITIALIZATION", "cyan")
    defense_system.print_colored("=" * 60, "cyan")
    
    startup_sequence = [
        "🔬 Initializing quantum processors...",
        "📡 Activating quantum radar array...",
        "📹 Calibrating quantum camera systems...",
        "🎯 Loading targeting algorithms...",
        "⚡ Establishing quantum field coherence...",
        "🔋 Quantum battery systems online...",
        "🌐 Quantum communication established...",
        "🛡️  Defense grid operational"
    ]
    
    for message in startup_sequence:
        defense_system.print_colored(f"[✓] {message}", "green")
        time.sleep(0.8)
    
    defense_system.print_colored("\n🎯 SYSTEM READY FOR AUTONOMOUS OPERATION", "bold")
    defense_system.print_colored("Starting operations in 3 seconds...", "yellow")
    time.sleep(3)

    # Start main system
    defense_system.run_system()

if __name__ == "__main__":
    main()
