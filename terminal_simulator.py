#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Realistic Terminal System Simulator
A complete terminal simulation with file system, processes, and network tools
"""

import os
import sys
import time
import random
import threading
from datetime import datetime
from collections import defaultdict

class FileSystemItem:
    def __init__(self, name, is_directory=False, content="", size=0):
        self.name = name
        self.is_directory = is_directory
        self.content = content
        self.size = size if size > 0 else len(content)
        self.last_modified = datetime.now()
        self.permissions = "drwxr-xr-x" if is_directory else "-rw-r--r--"
        self.children = {} if is_directory else None

class SystemProcess:
    def __init__(self, pid, name, cpu_usage=0.0, memory_usage=0.0):
        self.pid = pid
        self.name = name
        self.cpu_usage = cpu_usage
        self.memory_usage = memory_usage
        self.start_time = datetime.now()
        self.status = "Running"

class TerminalSimulator:
    def __init__(self):
        self.current_directory = "/home/<USER>"
        self.command_history = []
        self.processes = {}
        self.next_pid = 1000
        self.system_metrics = {
            'cpu': random.randint(20, 60),
            'memory': random.randint(40, 80),
            'disk': random.randint(30, 70),
            'network': random.randint(10, 100)
        }
        
        # Initialize file system
        self.root = self.create_file_system()
        self.current_node = self.get_node_by_path("/home/<USER>")
        
        # Initialize processes
        self.init_processes()
        
        # Start system metrics updater
        self.start_metrics_updater()
        
    def create_file_system(self):
        """Create a realistic file system structure"""
        root = FileSystemItem("/", True)
        
        # Create main directories
        home = FileSystemItem("home", True)
        etc = FileSystemItem("etc", True)
        var = FileSystemItem("var", True)
        usr = FileSystemItem("usr", True)
        bin_dir = FileSystemItem("bin", True)
        
        root.children = {
            "home": home,
            "etc": etc,
            "var": var,
            "usr": usr,
            "bin": bin_dir
        }
        
        # Create user directory
        user = FileSystemItem("user", True)
        documents = FileSystemItem("Documents", True)
        downloads = FileSystemItem("Downloads", True)
        desktop = FileSystemItem("Desktop", True)
        
        home.children = {"user": user}
        user.children = {
            "Documents": documents,
            "Downloads": downloads,
            "Desktop": desktop
        }
        
        # Add some files
        readme = FileSystemItem("readme.txt", False, 
            "Welcome to the System Simulator!\n\n"
            "This is a realistic terminal simulation.\n"
            "You can navigate the file system, run commands, and interact with the system.\n\n"
            "Features:\n"
            "- Complete terminal emulation\n"
            "- File system simulation\n"
            "- Process management\n"
            "- Network tools\n"
            "- System monitoring\n\n"
            "Enjoy exploring!")
        
        config = FileSystemItem("config.conf", False,
            "# System Configuration\n"
            "version=1.0\n"
            "debug=false\n"
            "max_connections=100\n"
            "timeout=30")
        
        user.children["readme.txt"] = readme
        user.children["config.conf"] = config
        
        return root
    
    def get_node_by_path(self, path):
        """Get file system node by path"""
        if path == "/":
            return self.root
        
        parts = [p for p in path.split("/") if p]
        current = self.root
        
        for part in parts:
            if current.children and part in current.children:
                current = current.children[part]
            else:
                return None
        
        return current
    
    def init_processes(self):
        """Initialize system processes"""
        system_processes = [
            ("init", 5.0, 50.0),
            ("systemd", 3.0, 80.0),
            ("NetworkManager", 2.0, 30.0),
            ("sshd", 1.0, 20.0),
            ("apache2", 8.0, 120.0),
            ("mysql", 15.0, 200.0),
            ("python3", 10.0, 150.0),
            ("terminal", 5.0, 40.0)
        ]
        
        for name, cpu, memory in system_processes:
            process = SystemProcess(self.next_pid, name, cpu, memory)
            self.processes[self.next_pid] = process
            self.next_pid += 1
    
    def start_metrics_updater(self):
        """Start background thread to update system metrics"""
        def update_metrics():
            while True:
                time.sleep(2)
                self.system_metrics['cpu'] += random.randint(-5, 5)
                self.system_metrics['memory'] += random.randint(-3, 3)
                self.system_metrics['disk'] += random.randint(-1, 1)
                self.system_metrics['network'] += random.randint(-20, 20)
                
                # Keep values in reasonable ranges
                for key in self.system_metrics:
                    if key == 'network':
                        self.system_metrics[key] = max(0, min(1000, self.system_metrics[key]))
                    else:
                        self.system_metrics[key] = max(5, min(95, self.system_metrics[key]))
        
        thread = threading.Thread(target=update_metrics, daemon=True)
        thread.start()
    
    def print_colored(self, text, color="white"):
        """Print colored text"""
        colors = {
            "red": "\033[91m",
            "green": "\033[92m",
            "yellow": "\033[93m",
            "blue": "\033[94m",
            "magenta": "\033[95m",
            "cyan": "\033[96m",
            "white": "\033[97m",
            "reset": "\033[0m"
        }
        print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}")
    
    def show_boot_sequence(self):
        """Show realistic boot sequence"""
        boot_messages = [
            "System Simulator v1.0 Starting...",
            "Loading kernel modules...",
            "Initializing hardware...",
            "Starting system services...",
            "Mounting file systems...",
            "Loading user interface...",
            "System ready!"
        ]
        
        self.print_colored("=" * 50, "cyan")
        self.print_colored("  REALISTIC SYSTEM SIMULATOR", "green")
        self.print_colored("=" * 50, "cyan")
        print()
        
        for message in boot_messages:
            self.print_colored(f"[  OK  ] {message}", "green")
            time.sleep(0.5)
        
        print()
        self.print_colored("Welcome to the System Simulator!", "yellow")
        self.print_colored("Type 'help' for available commands", "cyan")
        print()
    
    def get_prompt(self):
        """Get command prompt"""
        return f"\033[92muser@system\033[0m:\033[94m{self.current_directory}\033[0m$ "
    
    def execute_command(self, command_line):
        """Execute a command"""
        if not command_line.strip():
            return
        
        parts = command_line.strip().split()
        command = parts[0]
        args = parts[1:] if len(parts) > 1 else []
        
        # Add to history
        self.command_history.append(command_line)
        
        # Command mapping
        commands = {
            'help': self.cmd_help,
            'ls': self.cmd_ls,
            'dir': self.cmd_ls,
            'pwd': self.cmd_pwd,
            'cd': self.cmd_cd,
            'mkdir': self.cmd_mkdir,
            'touch': self.cmd_touch,
            'cat': self.cmd_cat,
            'echo': self.cmd_echo,
            'ps': self.cmd_ps,
            'top': self.cmd_top,
            'kill': self.cmd_kill,
            'ping': self.cmd_ping,
            'wget': self.cmd_wget,
            'history': self.cmd_history,
            'clear': self.cmd_clear,
            'date': self.cmd_date,
            'whoami': self.cmd_whoami,
            'uname': self.cmd_uname,
            'df': self.cmd_df,
            'free': self.cmd_free,
            'netstat': self.cmd_netstat,
            'ifconfig': self.cmd_ifconfig,
            'systemctl': self.cmd_systemctl,
            'service': self.cmd_service,
            'sudo': self.cmd_sudo,
            'chmod': self.cmd_chmod,
            'matrix': self.cmd_matrix,
            'exit': self.cmd_exit
        }
        
        if command in commands:
            try:
                commands[command](args)
            except Exception as e:
                self.print_colored(f"Error executing command: {e}", "red")
        else:
            self.print_colored(f"Command not found: {command}", "red")
            self.print_colored("Type 'help' for available commands", "yellow")
    
    def cmd_help(self, args):
        """Show help"""
        help_text = """
Available Commands:
==================
File System:
  ls/dir        - List directory contents
  pwd           - Show current directory
  cd <path>     - Change directory
  mkdir <name>  - Create directory
  touch <file>  - Create file
  cat <file>    - Show file content
  chmod <perm>  - Change permissions

System Information:
  ps            - Show running processes
  top           - System information
  kill <pid>    - Kill process
  date          - Show current date/time
  whoami        - Show current user
  uname         - System information
  df            - Disk usage
  free          - Memory usage

Network:
  ping <host>   - Ping network host
  wget <url>    - Download file
  netstat       - Network connections
  ifconfig      - Network configuration

System Control:
  systemctl     - System service control
  service       - Service management
  sudo <cmd>    - Execute as root

Utilities:
  echo <text>   - Display text
  history       - Show command history
  clear         - Clear screen
  matrix        - Matrix effect
  help          - Show this help
  exit          - Exit simulator
"""
        self.print_colored(help_text, "cyan")
    
    def cmd_ls(self, args):
        """List directory contents"""
        if not self.current_node or not self.current_node.is_directory:
            self.print_colored("Not a directory", "red")
            return
        
        print("total", len(self.current_node.children))
        
        for name, item in sorted(self.current_node.children.items()):
            permissions = item.permissions
            size = str(item.size).rjust(8)
            date = item.last_modified.strftime("%b %d %H:%M")
            
            if item.is_directory:
                self.print_colored(f"{permissions} 1 user user {size} {date} {name}/", "blue")
            else:
                print(f"{permissions} 1 user user {size} {date} {name}")
    
    def cmd_pwd(self, args):
        """Show current directory"""
        print(self.current_directory)
    
    def cmd_cd(self, args):
        """Change directory"""
        if not args:
            self.current_directory = "/home/<USER>"
            self.current_node = self.get_node_by_path(self.current_directory)
            return
        
        path = args[0]
        
        if path == "..":
            if self.current_directory != "/":
                parts = self.current_directory.rstrip("/").split("/")
                if len(parts) > 1:
                    new_path = "/".join(parts[:-1]) or "/"
                    self.current_directory = new_path
                    self.current_node = self.get_node_by_path(new_path)
        elif path.startswith("/"):
            node = self.get_node_by_path(path)
            if node and node.is_directory:
                self.current_directory = path
                self.current_node = node
            else:
                self.print_colored(f"cd: {path}: No such file or directory", "red")
        else:
            if self.current_node and path in self.current_node.children:
                item = self.current_node.children[path]
                if item.is_directory:
                    new_path = f"{self.current_directory.rstrip('/')}/{path}"
                    self.current_directory = new_path
                    self.current_node = item
                else:
                    self.print_colored(f"cd: {path}: Not a directory", "red")
            else:
                self.print_colored(f"cd: {path}: No such file or directory", "red")
    
    def cmd_mkdir(self, args):
        """Create directory"""
        if not args:
            self.print_colored("mkdir: missing operand", "red")
            return
        
        name = args[0]
        if self.current_node and name not in self.current_node.children:
            new_dir = FileSystemItem(name, True)
            self.current_node.children[name] = new_dir
            self.print_colored(f"Directory '{name}' created", "green")
        else:
            self.print_colored(f"mkdir: cannot create directory '{name}': File exists", "red")
    
    def cmd_touch(self, args):
        """Create file"""
        if not args:
            self.print_colored("touch: missing file operand", "red")
            return
        
        name = args[0]
        if self.current_node:
            if name in self.current_node.children:
                # Update timestamp
                self.current_node.children[name].last_modified = datetime.now()
            else:
                new_file = FileSystemItem(name, False, "")
                self.current_node.children[name] = new_file
            self.print_colored(f"File '{name}' created/updated", "green")
    
    def cmd_cat(self, args):
        """Show file content"""
        if not args:
            self.print_colored("cat: missing file operand", "red")
            return
        
        name = args[0]
        if self.current_node and name in self.current_node.children:
            item = self.current_node.children[name]
            if not item.is_directory:
                print(item.content)
            else:
                self.print_colored(f"cat: {name}: Is a directory", "red")
        else:
            self.print_colored(f"cat: {name}: No such file or directory", "red")
    
    def cmd_echo(self, args):
        """Echo text"""
        print(" ".join(args))
    
    def cmd_ps(self, args):
        """Show processes"""
        print("  PID TTY          TIME CMD")
        for pid, process in self.processes.items():
            runtime = datetime.now() - process.start_time
            time_str = f"{runtime.seconds//3600:02d}:{(runtime.seconds%3600)//60:02d}:{runtime.seconds%60:02d}"
            print(f"{pid:5d} pts/0    {time_str} {process.name}")
    
    def cmd_top(self, args):
        """Show system information"""
        self.print_colored("System Information:", "green")
        print(f"CPU Usage:    {self.system_metrics['cpu']:3d}%")
        print(f"Memory Usage: {self.system_metrics['memory']:3d}%")
        print(f"Disk Usage:   {self.system_metrics['disk']:3d}%")
        print(f"Network:      {self.system_metrics['network']:3d} KB/s")
        print(f"Processes:    {len(self.processes)}")
        print(f"Uptime:       {datetime.now().strftime('%H:%M:%S')}")
    
    def cmd_kill(self, args):
        """Kill process"""
        if not args:
            self.print_colored("kill: usage: kill <pid>", "red")
            return
        
        try:
            pid = int(args[0])
            if pid in self.processes:
                del self.processes[pid]
                self.print_colored(f"Process {pid} terminated", "green")
            else:
                self.print_colored(f"kill: ({pid}) - No such process", "red")
        except ValueError:
            self.print_colored("kill: invalid PID", "red")
    
    def cmd_ping(self, args):
        """Ping host"""
        if not args:
            self.print_colored("ping: usage: ping <hostname>", "red")
            return
        
        host = args[0]
        print(f"PING {host} (***********): 56 data bytes")
        
        for i in range(4):
            time.sleep(1)
            latency = random.randint(10, 100)
            print(f"64 bytes from {host}: icmp_seq={i+1} ttl=64 time={latency}ms")
        
        print(f"--- {host} ping statistics ---")
        self.print_colored("4 packets transmitted, 4 received, 0% packet loss", "green")
    
    def cmd_wget(self, args):
        """Download file"""
        if not args:
            self.print_colored("wget: missing URL", "red")
            return
        
        url = args[0]
        print(f"--{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}--  {url}")
        print("Resolving hostname... done.")
        print("Connecting to server... connected.")
        
        for i in range(0, 101, 10):
            time.sleep(0.2)
            print(f"Downloading... {i}%")
        
        self.print_colored("Download completed successfully!", "green")
    
    def cmd_history(self, args):
        """Show command history"""
        for i, cmd in enumerate(self.command_history, 1):
            print(f"{i:4d}  {cmd}")
    
    def cmd_clear(self, args):
        """Clear screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def cmd_date(self, args):
        """Show current date"""
        print(datetime.now().strftime("%a %b %d %H:%M:%S %Z %Y"))
    
    def cmd_whoami(self, args):
        """Show current user"""
        print("user")
    
    def cmd_uname(self, args):
        """Show system information"""
        if args and args[0] == "-a":
            print("Linux system 5.4.0-42-generic #46-Ubuntu SMP Fri Jul 10 00:24:02 UTC 2020 x86_64 x86_64 x86_64 GNU/Linux")
        else:
            print("Linux")
    
    def cmd_df(self, args):
        """Show disk usage"""
        print("Filesystem     1K-blocks    Used Available Use% Mounted on")
        print("/dev/sda1       20971520 9437184  10485760  48% /")
        print("tmpfs            2097152       0   2097152   0% /dev/shm")
    
    def cmd_free(self, args):
        """Show memory usage"""
        total = 8388608  # 8GB in KB
        used = int(total * self.system_metrics['memory'] / 100)
        free = total - used
        
        print("              total        used        free      shared  buff/cache   available")
        print(f"Mem:        {total:8d}  {used:8d}  {free:8d}           0           0  {free:8d}")
        print("Swap:              0           0           0")
    
    def cmd_netstat(self, args):
        """Show network connections"""
        print("Active Internet connections (w/o servers)")
        print("Proto Recv-Q Send-Q Local Address           Foreign Address         State")
        print("tcp        0      0 ***********00:22       ***********:54321       ESTABLISHED")
        print("tcp        0      0 ***********00:80       0.0.0.0:*               LISTEN")
    
    def cmd_ifconfig(self, args):
        """Show network configuration"""
        print("eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500")
        print("        inet ***********00  netmask *************  broadcast *************")
        print("        inet6 fe80::a00:27ff:fe4e:66a1  prefixlen 64  scopeid 0x20<link>")
        print("        ether 08:00:27:4e:66:a1  txqueuelen 1000  (Ethernet)")
    
    def cmd_systemctl(self, args):
        """System control"""
        if not args:
            self.print_colored("systemctl: missing command", "red")
            return
        
        action = args[0]
        service = args[1] if len(args) > 1 else "unknown"
        
        if action == "status":
            self.print_colored(f"● {service}.service - {service} Service", "green")
            print("   Loaded: loaded (/etc/systemd/system/service.service; enabled)")
            self.print_colored("   Active: active (running)", "green")
        elif action in ["start", "stop", "restart"]:
            self.print_colored(f"{action.capitalize()}ed {service} service", "green")
        else:
            self.print_colored(f"Unknown systemctl command: {action}", "red")
    
    def cmd_service(self, args):
        """Service control"""
        if len(args) < 2:
            self.print_colored("service: usage: service <service> <action>", "red")
            return
        
        service_name = args[0]
        action = args[1]
        self.print_colored(f"{service_name}: {action}", "green")
    
    def cmd_sudo(self, args):
        """Execute as root"""
        if not args:
            self.print_colored("sudo: a command is required", "red")
            return
        
        self.print_colored("[sudo] password for user: ", "yellow", end="")
        input()  # Simulate password input
        
        command = " ".join(args)
        self.print_colored(f"Executing as root: {command}", "green")
        self.execute_command(command)
    
    def cmd_chmod(self, args):
        """Change permissions"""
        if len(args) < 2:
            self.print_colored("chmod: missing operand", "red")
            return
        
        permissions = args[0]
        filename = args[1]
        self.print_colored(f"Changed permissions of '{filename}' to {permissions}", "green")
    
    def cmd_matrix(self, args):
        """Matrix effect"""
        self.print_colored("Entering the Matrix...", "green")
        
        try:
            for _ in range(20):
                line = ""
                for _ in range(80):
                    if random.random() < 0.1:
                        line += chr(random.randint(33, 126))
                    else:
                        line += " "
                self.print_colored(line, "green")
                time.sleep(0.1)
        except KeyboardInterrupt:
            pass
        
        self.print_colored("Exiting the Matrix...", "green")
    
    def cmd_exit(self, args):
        """Exit simulator"""
        self.print_colored("Goodbye!", "green")
        sys.exit(0)
    
    def run(self):
        """Main run loop"""
        self.show_boot_sequence()
        
        try:
            while True:
                try:
                    command = input(self.get_prompt())
                    self.execute_command(command)
                except KeyboardInterrupt:
                    print()
                    self.print_colored("Use 'exit' to quit", "yellow")
                except EOFError:
                    break
        except KeyboardInterrupt:
            print()
            self.print_colored("Goodbye!", "green")

if __name__ == "__main__":
    simulator = TerminalSimulator()
    simulator.run()
