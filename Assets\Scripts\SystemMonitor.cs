using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class SystemMonitor : MonoBehaviour
{
    [Header("Performance Graphs")]
    public LineRenderer cpuGraph;
    public LineRenderer memoryGraph;
    public LineRenderer diskGraph;
    public LineRenderer networkGraph;
    
    [Header("Performance Labels")]
    public TMP_Text cpuLabel;
    public TMP_Text memoryLabel;
    public TMP_Text diskLabel;
    public TMP_Text networkLabel;
    
    [Header("Process List")]
    public Transform processListContainer;
    public GameObject processItemPrefab;
    public ScrollRect processScrollRect;
    
    [Header("System Info")]
    public TMP_Text systemInfoText;
    public TMP_Text uptimeText;
    
    [Header("Graph Settings")]
    public int maxDataPoints = 60;
    public float updateInterval = 1f;
    public Color cpuColor = Color.red;
    public Color memoryColor = Color.green;
    public Color diskColor = Color.blue;
    public Color networkColor = Color.yellow;
    
    private SystemSimulator systemSimulator;
    private List<float> cpuData = new List<float>();
    private List<float> memoryData = new List<float>();
    private List<float> diskData = new List<float>();
    private List<float> networkData = new List<float>();
    private List<GameObject> processItems = new List<GameObject>();
    private System.DateTime startTime;
    
    void Start()
    {
        systemSimulator = FindObjectOfType<SystemSimulator>();
        startTime = System.DateTime.Now;
        
        InitializeGraphs();
        StartCoroutine(UpdateMonitor());
        RefreshProcessList();
    }
    
    void InitializeGraphs()
    {
        if (cpuGraph != null)
        {
            cpuGraph.color = cpuColor;
            cpuGraph.positionCount = 0;
        }
        
        if (memoryGraph != null)
        {
            memoryGraph.color = memoryColor;
            memoryGraph.positionCount = 0;
        }
        
        if (diskGraph != null)
        {
            diskGraph.color = diskColor;
            diskGraph.positionCount = 0;
        }
        
        if (networkGraph != null)
        {
            networkGraph.color = networkColor;
            networkGraph.positionCount = 0;
        }
    }
    
    IEnumerator UpdateMonitor()
    {
        while (true)
        {
            UpdatePerformanceData();
            UpdateGraphs();
            UpdateLabels();
            UpdateSystemInfo();
            
            yield return new WaitForSeconds(updateInterval);
        }
    }
    
    void UpdatePerformanceData()
    {
        if (systemSimulator == null) return;
        
        var metrics = systemSimulator.GetSystemMetrics();
        
        // Add new data points
        cpuData.Add(metrics["CPU"]);
        memoryData.Add(metrics["Memory"]);
        diskData.Add(metrics["Disk"]);
        networkData.Add(metrics["Network"] / 10f); // Scale network for display
        
        // Remove old data points
        if (cpuData.Count > maxDataPoints)
        {
            cpuData.RemoveAt(0);
            memoryData.RemoveAt(0);
            diskData.RemoveAt(0);
            networkData.RemoveAt(0);
        }
    }
    
    void UpdateGraphs()
    {
        UpdateGraph(cpuGraph, cpuData);
        UpdateGraph(memoryGraph, memoryData);
        UpdateGraph(diskGraph, diskData);
        UpdateGraph(networkGraph, networkData);
    }
    
    void UpdateGraph(LineRenderer graph, List<float> data)
    {
        if (graph == null || data.Count == 0) return;
        
        graph.positionCount = data.Count;
        
        for (int i = 0; i < data.Count; i++)
        {
            float x = (float)i / (maxDataPoints - 1) * 2f - 1f; // Normalize to -1 to 1
            float y = (data[i] / 100f) * 2f - 1f; // Normalize percentage to -1 to 1
            graph.SetPosition(i, new Vector3(x, y, 0));
        }
    }
    
    void UpdateLabels()
    {
        if (systemSimulator == null) return;
        
        var metrics = systemSimulator.GetSystemMetrics();
        
        if (cpuLabel != null)
            cpuLabel.text = $"CPU: {metrics["CPU"]:F1}%";
        
        if (memoryLabel != null)
            memoryLabel.text = $"Memory: {metrics["Memory"]:F1}%";
        
        if (diskLabel != null)
            diskLabel.text = $"Disk: {metrics["Disk"]:F1}%";
        
        if (networkLabel != null)
            networkLabel.text = $"Network: {metrics["Network"]:F1} KB/s";
    }
    
    void UpdateSystemInfo()
    {
        if (systemInfoText != null)
        {
            var info = "System Information:\n";
            info += $"OS: Ubuntu 20.04 LTS\n";
            info += $"Kernel: 5.4.0-42-generic\n";
            info += $"Architecture: x86_64\n";
            info += $"Processor: Intel Core i7-9700K\n";
            info += $"Memory: 16 GB DDR4\n";
            info += $"Graphics: NVIDIA GeForce RTX 3070\n";
            
            systemInfoText.text = info;
        }
        
        if (uptimeText != null)
        {
            var uptime = System.DateTime.Now - startTime;
            uptimeText.text = $"Uptime: {uptime.Days}d {uptime.Hours}h {uptime.Minutes}m";
        }
    }
    
    public void RefreshProcessList()
    {
        ClearProcessList();
        
        if (systemSimulator == null) return;
        
        var processes = systemSimulator.GetRunningProcesses();
        
        foreach (var process in processes)
        {
            CreateProcessItem(process);
        }
    }
    
    void CreateProcessItem(SystemProcess process)
    {
        if (processItemPrefab == null || processListContainer == null) return;
        
        GameObject item = Instantiate(processItemPrefab, processListContainer);
        processItems.Add(item);
        
        // Set up process item components
        var pidText = item.transform.Find("PIDText")?.GetComponent<TMP_Text>();
        var nameText = item.transform.Find("NameText")?.GetComponent<TMP_Text>();
        var cpuText = item.transform.Find("CPUText")?.GetComponent<TMP_Text>();
        var memoryText = item.transform.Find("MemoryText")?.GetComponent<TMP_Text>();
        var statusText = item.transform.Find("StatusText")?.GetComponent<TMP_Text>();
        
        if (pidText != null)
            pidText.text = process.PID.ToString();
        
        if (nameText != null)
            nameText.text = process.Name;
        
        if (cpuText != null)
            cpuText.text = $"{process.CPUUsage:F1}%";
        
        if (memoryText != null)
            memoryText.text = $"{process.MemoryUsage:F0} MB";
        
        if (statusText != null)
            statusText.text = process.Status;
        
        // Add click handler for process selection
        var button = item.GetComponent<Button>();
        if (button == null)
            button = item.AddComponent<Button>();
        
        button.onClick.AddListener(() => SelectProcess(process, item));
    }
    
    void SelectProcess(SystemProcess process, GameObject itemUI)
    {
        // Deselect previous items
        foreach (var item in processItems)
        {
            var image = item.GetComponent<Image>();
            if (image != null)
                image.color = Color.white;
        }
        
        // Select current item
        var selectedImage = itemUI.GetComponent<Image>();
        if (selectedImage != null)
            selectedImage.color = Color.blue;
        
        Debug.Log($"Selected process: {process.Name} (PID: {process.PID})");
    }
    
    void ClearProcessList()
    {
        foreach (var item in processItems)
        {
            if (item != null)
                Destroy(item);
        }
        processItems.Clear();
    }
    
    public void KillSelectedProcess()
    {
        // This would be implemented to kill the selected process
        Debug.Log("Kill process functionality would be implemented here");
    }
    
    public void SortProcesses(int sortType)
    {
        // Sort processes by different criteria
        // 0 = PID, 1 = Name, 2 = CPU, 3 = Memory
        RefreshProcessList();
    }
    
    void Update()
    {
        // Handle keyboard shortcuts
        if (Input.GetKeyDown(KeyCode.F5))
        {
            RefreshProcessList();
        }
    }
}
