using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class SceneSetup : MonoBehaviour
{
    [Header("Prefab References")]
    public GameObject canvasPrefab;
    public GameObject terminalWindowPrefab;
    public GameObject fileManagerWindowPrefab;
    public GameObject systemMonitorWindowPrefab;
    
    [Header("UI Colors")]
    public Color backgroundColor = new Color(0.1f, 0.1f, 0.1f, 1f);
    public Color windowColor = new Color(0.2f, 0.2f, 0.2f, 1f);
    public Color taskbarColor = new Color(0.15f, 0.15f, 0.15f, 1f);
    public Color textColor = Color.white;
    
    void Start()
    {
        SetupScene();
    }
    
    void SetupScene()
    {
        // Create main canvas if it doesn't exist
        Canvas mainCanvas = FindObjectOfType<Canvas>();
        if (mainCanvas == null)
        {
            CreateMainCanvas();
        }
        
        // Setup camera
        SetupCamera();
        
        // Create UI elements
        CreateDesktop();
        CreateTaskbar();
        CreateWindowPrefabs();
        
        // Setup systems
        SetupSystemComponents();
        
        Debug.Log("Scene setup complete! Press F1 for Terminal, F2 for File Manager, F3 for System Monitor");
    }
    
    void CreateMainCanvas()
    {
        GameObject canvasObj = new GameObject("Main Canvas");
        Canvas canvas = canvasObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.sortingOrder = 0;
        
        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.matchWidthOrHeight = 0.5f;
        
        canvasObj.AddComponent<GraphicRaycaster>();
    }
    
    void SetupCamera()
    {
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GameObject cameraObj = new GameObject("Main Camera");
            mainCamera = cameraObj.AddComponent<Camera>();
            cameraObj.tag = "MainCamera";
        }
        
        mainCamera.backgroundColor = backgroundColor;
        mainCamera.clearFlags = CameraClearFlags.SolidColor;
    }
    
    void CreateDesktop()
    {
        Canvas canvas = FindObjectOfType<Canvas>();
        
        // Create desktop background
        GameObject desktop = new GameObject("Desktop");
        desktop.transform.SetParent(canvas.transform, false);
        
        RectTransform desktopRect = desktop.AddComponent<RectTransform>();
        desktopRect.anchorMin = Vector2.zero;
        desktopRect.anchorMax = Vector2.one;
        desktopRect.offsetMin = Vector2.zero;
        desktopRect.offsetMax = Vector2.zero;
        
        Image desktopImage = desktop.AddComponent<Image>();
        desktopImage.color = backgroundColor;
        
        Button desktopButton = desktop.AddComponent<Button>();
        desktopButton.transition = Selectable.Transition.None;
        
        // Create desktop icon container
        GameObject iconContainer = new GameObject("Desktop Icons");
        iconContainer.transform.SetParent(desktop.transform, false);
        
        RectTransform iconRect = iconContainer.AddComponent<RectTransform>();
        iconRect.anchorMin = Vector2.zero;
        iconRect.anchorMax = Vector2.one;
        iconRect.offsetMin = Vector2.zero;
        iconRect.offsetMax = new Vector2(0, -60); // Leave space for taskbar
    }
    
    void CreateTaskbar()
    {
        Canvas canvas = FindObjectOfType<Canvas>();
        
        // Create taskbar
        GameObject taskbar = new GameObject("Taskbar");
        taskbar.transform.SetParent(canvas.transform, false);
        
        RectTransform taskbarRect = taskbar.AddComponent<RectTransform>();
        taskbarRect.anchorMin = new Vector2(0, 0);
        taskbarRect.anchorMax = new Vector2(1, 0);
        taskbarRect.anchoredPosition = Vector2.zero;
        taskbarRect.sizeDelta = new Vector2(0, 60);
        
        Image taskbarImage = taskbar.AddComponent<Image>();
        taskbarImage.color = taskbarColor;
        
        // Create start button
        CreateStartButton(taskbar);
        
        // Create taskbar icon container
        GameObject iconContainer = new GameObject("Taskbar Icons");
        iconContainer.transform.SetParent(taskbar.transform, false);
        
        RectTransform iconContainerRect = iconContainer.AddComponent<RectTransform>();
        iconContainerRect.anchorMin = new Vector2(0.1f, 0);
        iconContainerRect.anchorMax = new Vector2(0.8f, 1);
        iconContainerRect.offsetMin = Vector2.zero;
        iconContainerRect.offsetMax = Vector2.zero;
        
        HorizontalLayoutGroup layoutGroup = iconContainer.AddComponent<HorizontalLayoutGroup>();
        layoutGroup.spacing = 5;
        layoutGroup.padding = new RectOffset(10, 10, 5, 5);
        
        // Create system info area
        CreateSystemInfoArea(taskbar);
    }
    
    void CreateStartButton(GameObject taskbar)
    {
        GameObject startButton = new GameObject("Start Button");
        startButton.transform.SetParent(taskbar.transform, false);
        
        RectTransform startRect = startButton.AddComponent<RectTransform>();
        startRect.anchorMin = new Vector2(0, 0);
        startRect.anchorMax = new Vector2(0, 1);
        startRect.anchoredPosition = Vector2.zero;
        startRect.sizeDelta = new Vector2(80, 0);
        
        Image startImage = startButton.AddComponent<Image>();
        startImage.color = new Color(0.3f, 0.3f, 0.3f, 1f);
        
        Button button = startButton.AddComponent<Button>();
        
        // Add start button text
        GameObject startText = new GameObject("Start Text");
        startText.transform.SetParent(startButton.transform, false);
        
        RectTransform textRect = startText.AddComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        TMP_Text text = startText.AddComponent<TextMeshProUGUI>();
        text.text = "Start";
        text.color = textColor;
        text.fontSize = 14;
        text.alignment = TextAlignmentOptions.Center;
    }
    
    void CreateSystemInfoArea(GameObject taskbar)
    {
        GameObject infoArea = new GameObject("System Info");
        infoArea.transform.SetParent(taskbar.transform, false);
        
        RectTransform infoRect = infoArea.AddComponent<RectTransform>();
        infoRect.anchorMin = new Vector2(0.8f, 0);
        infoRect.anchorMax = new Vector2(1, 1);
        infoRect.offsetMin = Vector2.zero;
        infoRect.offsetMax = Vector2.zero;
        
        // Create clock
        GameObject clock = new GameObject("Clock");
        clock.transform.SetParent(infoArea.transform, false);
        
        RectTransform clockRect = clock.AddComponent<RectTransform>();
        clockRect.anchorMin = new Vector2(0.5f, 0);
        clockRect.anchorMax = new Vector2(1, 1);
        clockRect.offsetMin = Vector2.zero;
        clockRect.offsetMax = Vector2.zero;
        
        TMP_Text clockText = clock.AddComponent<TextMeshProUGUI>();
        clockText.text = System.DateTime.Now.ToString("HH:mm:ss");
        clockText.color = textColor;
        clockText.fontSize = 12;
        clockText.alignment = TextAlignmentOptions.Center;
        
        // Create system stats
        GameObject stats = new GameObject("System Stats");
        stats.transform.SetParent(infoArea.transform, false);
        
        RectTransform statsRect = stats.AddComponent<RectTransform>();
        statsRect.anchorMin = new Vector2(0, 0);
        statsRect.anchorMax = new Vector2(0.5f, 1);
        statsRect.offsetMin = Vector2.zero;
        statsRect.offsetMax = Vector2.zero;
        
        TMP_Text statsText = stats.AddComponent<TextMeshProUGUI>();
        statsText.text = "CPU: 25% | RAM: 60%";
        statsText.color = textColor;
        statsText.fontSize = 10;
        statsText.alignment = TextAlignmentOptions.Center;
    }
    
    void CreateWindowPrefabs()
    {
        // Create terminal window prefab
        if (terminalWindowPrefab == null)
        {
            terminalWindowPrefab = CreateTerminalWindow();
        }
        
        // Create file manager window prefab
        if (fileManagerWindowPrefab == null)
        {
            fileManagerWindowPrefab = CreateFileManagerWindow();
        }
        
        // Create system monitor window prefab
        if (systemMonitorWindowPrefab == null)
        {
            systemMonitorWindowPrefab = CreateSystemMonitorWindow();
        }
    }
    
    GameObject CreateTerminalWindow()
    {
        GameObject window = CreateBaseWindow("Terminal Window", new Vector2(800, 600));
        
        // Add terminal-specific components
        GameObject content = window.transform.Find("Content").gameObject;
        
        // Create output area
        GameObject outputArea = new GameObject("Output Area");
        outputArea.transform.SetParent(content.transform, false);
        
        RectTransform outputRect = outputArea.AddComponent<RectTransform>();
        outputRect.anchorMin = Vector2.zero;
        outputRect.anchorMax = new Vector2(1, 0.9f);
        outputRect.offsetMin = Vector2.zero;
        outputRect.offsetMax = Vector2.zero;
        
        ScrollRect scrollRect = outputArea.AddComponent<ScrollRect>();
        scrollRect.vertical = true;
        scrollRect.horizontal = false;
        
        GameObject viewport = new GameObject("Viewport");
        viewport.transform.SetParent(outputArea.transform, false);
        viewport.AddComponent<RectTransform>();
        viewport.AddComponent<Image>().color = Color.black;
        viewport.AddComponent<Mask>();
        
        GameObject outputText = new GameObject("Output Text");
        outputText.transform.SetParent(viewport.transform, false);
        TMP_Text text = outputText.AddComponent<TextMeshProUGUI>();
        text.color = Color.green;
        text.fontSize = 12;
        text.font = Resources.GetBuiltinResource<TMP_FontAsset>("LiberationSans SDF");
        
        scrollRect.viewport = viewport.GetComponent<RectTransform>();
        scrollRect.content = outputText.GetComponent<RectTransform>();
        
        // Create input field
        GameObject inputField = new GameObject("Input Field");
        inputField.transform.SetParent(content.transform, false);
        
        RectTransform inputRect = inputField.AddComponent<RectTransform>();
        inputRect.anchorMin = new Vector2(0, 0.9f);
        inputRect.anchorMax = Vector2.one;
        inputRect.offsetMin = Vector2.zero;
        inputRect.offsetMax = Vector2.zero;
        
        TMP_InputField input = inputField.AddComponent<TMP_InputField>();
        input.textComponent = inputField.AddComponent<TextMeshProUGUI>();
        input.textComponent.color = Color.green;
        input.textComponent.fontSize = 12;
        
        // Add terminal system component
        TerminalSystem terminal = window.AddComponent<TerminalSystem>();
        terminal.commandInput = input;
        terminal.outputText = text;
        terminal.scrollRect = scrollRect;
        terminal.terminalWindow = window;
        
        window.SetActive(false);
        return window;
    }
    
    GameObject CreateFileManagerWindow()
    {
        GameObject window = CreateBaseWindow("File Manager", new Vector2(900, 700));
        window.SetActive(false);
        return window;
    }
    
    GameObject CreateSystemMonitorWindow()
    {
        GameObject window = CreateBaseWindow("System Monitor", new Vector2(1000, 800));
        window.SetActive(false);
        return window;
    }
    
    GameObject CreateBaseWindow(string title, Vector2 size)
    {
        Canvas canvas = FindObjectOfType<Canvas>();
        
        GameObject window = new GameObject(title);
        window.transform.SetParent(canvas.transform, false);
        
        RectTransform windowRect = window.AddComponent<RectTransform>();
        windowRect.sizeDelta = size;
        windowRect.anchoredPosition = Vector2.zero;
        
        Image windowImage = window.AddComponent<Image>();
        windowImage.color = windowColor;
        
        // Create title bar
        GameObject titleBar = new GameObject("Title Bar");
        titleBar.transform.SetParent(window.transform, false);
        
        RectTransform titleRect = titleBar.AddComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0, 1);
        titleRect.anchorMax = Vector2.one;
        titleRect.anchoredPosition = Vector2.zero;
        titleRect.sizeDelta = new Vector2(0, 30);
        
        Image titleImage = titleBar.AddComponent<Image>();
        titleImage.color = new Color(0.3f, 0.3f, 0.3f, 1f);
        
        WindowDragger dragger = titleBar.AddComponent<WindowDragger>();
        
        // Add title text
        GameObject titleText = new GameObject("Title Text");
        titleText.transform.SetParent(titleBar.transform, false);
        
        RectTransform titleTextRect = titleText.AddComponent<RectTransform>();
        titleTextRect.anchorMin = Vector2.zero;
        titleTextRect.anchorMax = new Vector2(0.8f, 1);
        titleTextRect.offsetMin = new Vector2(10, 0);
        titleTextRect.offsetMax = Vector2.zero;
        
        TMP_Text text = titleText.AddComponent<TextMeshProUGUI>();
        text.text = title;
        text.color = textColor;
        text.fontSize = 14;
        text.alignment = TextAlignmentOptions.CenterLeft;
        
        // Create close button
        GameObject closeButton = new GameObject("Close Button");
        closeButton.transform.SetParent(titleBar.transform, false);
        
        RectTransform closeRect = closeButton.AddComponent<RectTransform>();
        closeRect.anchorMin = new Vector2(1, 0);
        closeRect.anchorMax = Vector2.one;
        closeRect.anchoredPosition = Vector2.zero;
        closeRect.sizeDelta = new Vector2(30, 30);
        
        Image closeImage = closeButton.AddComponent<Image>();
        closeImage.color = Color.red;
        
        Button closeBtn = closeButton.AddComponent<Button>();
        closeBtn.onClick.AddListener(() => window.SetActive(false));
        
        // Create content area
        GameObject content = new GameObject("Content");
        content.transform.SetParent(window.transform, false);
        
        RectTransform contentRect = content.AddComponent<RectTransform>();
        contentRect.anchorMin = Vector2.zero;
        contentRect.anchorMax = new Vector2(1, 1);
        contentRect.offsetMin = Vector2.zero;
        contentRect.offsetMax = new Vector2(0, -30);
        
        return window;
    }
    
    void SetupSystemComponents()
    {
        // Create system simulator
        GameObject systemObj = new GameObject("System Simulator");
        SystemSimulator simulator = systemObj.AddComponent<SystemSimulator>();
        
        // Create UI manager
        GameObject uiObj = new GameObject("UI Manager");
        UIManager uiManager = uiObj.AddComponent<UIManager>();
        
        // Connect references
        Canvas canvas = FindObjectOfType<Canvas>();
        uiManager.mainCanvas = canvas;
        uiManager.desktopPanel = GameObject.Find("Desktop");
        uiManager.taskbar = GameObject.Find("Taskbar");
        uiManager.windowContainer = canvas.transform;
        uiManager.terminalWindowPrefab = terminalWindowPrefab;
        uiManager.fileManagerWindowPrefab = fileManagerWindowPrefab;
        uiManager.systemMonitorWindowPrefab = systemMonitorWindowPrefab;
        uiManager.systemSimulator = simulator;
        
        // Find UI elements
        uiManager.startButton = GameObject.Find("Start Button")?.GetComponent<Button>();
        uiManager.clockText = GameObject.Find("Clock")?.GetComponent<TMP_Text>();
        uiManager.systemStatsText = GameObject.Find("System Stats")?.GetComponent<TMP_Text>();
        uiManager.desktopIconContainer = GameObject.Find("Desktop Icons")?.transform;
        uiManager.taskbarIconContainer = GameObject.Find("Taskbar Icons")?.transform;
        
        // Create visual effects
        GameObject effectsObj = new GameObject("Visual Effects");
        VisualEffects effects = effectsObj.AddComponent<VisualEffects>();
        
        Debug.Log("System components setup complete!");
    }
}
