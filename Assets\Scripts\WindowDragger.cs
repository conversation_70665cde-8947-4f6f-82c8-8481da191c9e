using UnityEngine;
using UnityEngine.EventSystems;

public class WindowDragger : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IBeginDrag<PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IEndDragHandler
{
    private RectTransform rectTransform;
    private Canvas canvas;
    private Vector2 originalPosition;
    
    void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        canvas = GetComponentInParent<Canvas>();
    }
    
    public void OnBeginDrag(PointerEventData eventData)
    {
        originalPosition = rectTransform.anchoredPosition;
        
        // Bring window to front
        transform.SetAsLastSibling();
    }
    
    public void OnDrag(PointerEventData eventData)
    {
        if (rectTransform != null && canvas != null)
        {
            Vector2 deltaPosition = eventData.delta / canvas.scaleFactor;
            rectTransform.anchoredPosition += deltaPosition;
        }
    }
    
    public void OnEndDrag(PointerEventData eventData)
    {
        // Optional: Add window snapping or boundary checking here
        ClampToScreen();
    }
    
    void ClampToScreen()
    {
        if (canvas == null) return;
        
        Vector3[] corners = new Vector3[4];
        rectTransform.GetWorldCorners(corners);
        
        Rect canvasRect = canvas.GetComponent<RectTransform>().rect;
        
        // Get current position
        Vector2 currentPos = rectTransform.anchoredPosition;
        
        // Calculate boundaries
        float minX = -canvasRect.width / 2 + rectTransform.rect.width / 2;
        float maxX = canvasRect.width / 2 - rectTransform.rect.width / 2;
        float minY = -canvasRect.height / 2 + rectTransform.rect.height / 2;
        float maxY = canvasRect.height / 2 - rectTransform.rect.height / 2;
        
        // Clamp position
        currentPos.x = Mathf.Clamp(currentPos.x, minX, maxX);
        currentPos.y = Mathf.Clamp(currentPos.y, minY, maxY);
        
        rectTransform.anchoredPosition = currentPos;
    }
}
