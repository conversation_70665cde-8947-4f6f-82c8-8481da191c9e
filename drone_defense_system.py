#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Drone Defense System Simulator
Real-time drone detection, tracking, and engagement simulation
"""

import os
import sys
import time
import random
import threading
import math
from datetime import datetime
from collections import defaultdict

class Drone:
    def __init__(self, drone_id, x, y, altitude, speed, heading):
        self.id = drone_id
        self.x = x
        self.y = y
        self.altitude = altitude
        self.speed = speed  # km/h
        self.heading = heading  # degrees
        self.detected = False
        self.tracked = False
        self.threat_level = random.choice(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
        self.size = random.choice(['SMALL', 'MEDIUM', 'LARGE'])
        self.type = random.choice(['CIVILIAN', 'MILITARY', 'UNKNOWN', 'HOSTILE'])
        self.last_seen = datetime.now()
        self.engaged = False
        self.destroyed = False
        
    def update_position(self, dt):
        if not self.destroyed:
            # Convert heading to radians and update position
            heading_rad = math.radians(self.heading)
            distance = (self.speed * dt) / 3600  # km
            
            self.x += distance * math.sin(heading_rad)
            self.y += distance * math.cos(heading_rad)
            
            # Add some random movement
            self.heading += random.uniform(-5, 5)
            self.altitude += random.uniform(-10, 10)
            self.altitude = max(50, min(5000, self.altitude))  # Keep within bounds
            
            self.last_seen = datetime.now()

class DefenseSystem:
    def __init__(self):
        self.drones = {}
        self.radar_range = 50  # km
        self.missile_range = 30  # km
        self.system_status = "OPERATIONAL"
        self.threat_level = "GREEN"
        self.missiles_available = 24
        self.radar_active = True
        self.auto_engage = False
        self.engagement_log = []
        
        # System coordinates (center)
        self.base_x = 0
        self.base_y = 0
        
        # Start background processes
        self.start_radar_sweep()
        self.start_drone_generator()
        self.start_tracking_system()
        
    def print_colored(self, text, color="white"):
        """Print colored text"""
        colors = {
            "red": "\033[91m",
            "green": "\033[92m",
            "yellow": "\033[93m",
            "blue": "\033[94m",
            "magenta": "\033[95m",
            "cyan": "\033[96m",
            "white": "\033[97m",
            "reset": "\033[0m",
            "bold": "\033[1m"
        }
        print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}")
    
    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def calculate_distance(self, x1, y1, x2, y2):
        return math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
    
    def calculate_bearing(self, x1, y1, x2, y2):
        angle = math.degrees(math.atan2(x2 - x1, y2 - y1))
        return (angle + 360) % 360
    
    def start_radar_sweep(self):
        def radar_sweep():
            while True:
                if self.radar_active:
                    for drone in self.drones.values():
                        distance = self.calculate_distance(
                            self.base_x, self.base_y, drone.x, drone.y
                        )
                        
                        if distance <= self.radar_range and not drone.destroyed:
                            if not drone.detected:
                                drone.detected = True
                                self.log_event(f"RADAR: New contact detected - ID:{drone.id}")
                            
                            # Detection probability based on size and distance
                            detection_prob = 0.9
                            if drone.size == "SMALL":
                                detection_prob = 0.7
                            if distance > self.radar_range * 0.8:
                                detection_prob *= 0.6
                                
                            if random.random() < detection_prob:
                                drone.tracked = True
                        else:
                            drone.detected = False
                            drone.tracked = False
                
                time.sleep(2)  # Radar sweep every 2 seconds
        
        thread = threading.Thread(target=radar_sweep, daemon=True)
        thread.start()
    
    def start_drone_generator(self):
        def generate_drones():
            drone_id = 1
            while True:
                # Generate new drone every 10-30 seconds
                time.sleep(random.uniform(10, 30))
                
                # Random spawn position at edge of extended area
                angle = random.uniform(0, 360)
                distance = random.uniform(60, 100)  # km from base
                
                x = self.base_x + distance * math.sin(math.radians(angle))
                y = self.base_y + distance * math.cos(math.radians(angle))
                
                altitude = random.uniform(100, 3000)  # meters
                speed = random.uniform(50, 300)  # km/h
                heading = random.uniform(0, 360)  # degrees
                
                drone = Drone(drone_id, x, y, altitude, speed, heading)
                self.drones[drone_id] = drone
                
                drone_id += 1
                
                # Remove old destroyed drones
                self.cleanup_drones()
        
        thread = threading.Thread(target=generate_drones, daemon=True)
        thread.start()
    
    def start_tracking_system(self):
        def update_tracking():
            while True:
                current_time = datetime.now()
                
                for drone in list(self.drones.values()):
                    if not drone.destroyed:
                        drone.update_position(1)  # 1 second update
                        
                        # Check if drone is still in area
                        distance = self.calculate_distance(
                            self.base_x, self.base_y, drone.x, drone.y
                        )
                        
                        if distance > 150:  # Remove if too far
                            del self.drones[drone.id]
                            continue
                        
                        # Auto-engage hostile drones if enabled
                        if (self.auto_engage and drone.tracked and 
                            drone.type == "HOSTILE" and not drone.engaged and
                            distance <= self.missile_range):
                            self.engage_target(drone.id)
                
                # Update threat level
                self.update_threat_level()
                time.sleep(1)
        
        thread = threading.Thread(target=update_tracking, daemon=True)
        thread.start()
    
    def cleanup_drones(self):
        # Remove drones that are too old or destroyed
        current_time = datetime.now()
        to_remove = []
        
        for drone_id, drone in self.drones.items():
            if drone.destroyed:
                time_since_destruction = (current_time - drone.last_seen).seconds
                if time_since_destruction > 30:  # Remove after 30 seconds
                    to_remove.append(drone_id)
        
        for drone_id in to_remove:
            del self.drones[drone_id]
    
    def update_threat_level(self):
        hostile_count = sum(1 for d in self.drones.values() 
                          if d.type == "HOSTILE" and d.tracked and not d.destroyed)
        
        if hostile_count == 0:
            self.threat_level = "GREEN"
        elif hostile_count <= 2:
            self.threat_level = "YELLOW"
        elif hostile_count <= 5:
            self.threat_level = "ORANGE"
        else:
            self.threat_level = "RED"
    
    def log_event(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.engagement_log.append(f"[{timestamp}] {message}")
        if len(self.engagement_log) > 50:
            self.engagement_log.pop(0)
    
    def engage_target(self, drone_id):
        if drone_id not in self.drones:
            return False, "Target not found"
        
        drone = self.drones[drone_id]
        
        if drone.destroyed:
            return False, "Target already destroyed"
        
        if self.missiles_available <= 0:
            return False, "No missiles available"
        
        distance = self.calculate_distance(
            self.base_x, self.base_y, drone.x, drone.y
        )
        
        if distance > self.missile_range:
            return False, f"Target out of range ({distance:.1f}km > {self.missile_range}km)"
        
        # Calculate hit probability
        hit_probability = 0.85
        if drone.size == "SMALL":
            hit_probability = 0.75
        if distance > self.missile_range * 0.7:
            hit_probability *= 0.8
        if drone.speed > 200:
            hit_probability *= 0.9
        
        drone.engaged = True
        self.missiles_available -= 1
        
        # Simulate missile flight time
        flight_time = distance / 2  # Rough calculation (2 km/s missile speed)
        
        self.log_event(f"MISSILE: Engaging target ID:{drone_id} at {distance:.1f}km")
        
        # Simulate engagement result after flight time
        def engagement_result():
            time.sleep(min(flight_time, 5))  # Max 5 second delay for simulation
            
            if random.random() < hit_probability:
                drone.destroyed = True
                self.log_event(f"HIT: Target ID:{drone_id} destroyed")
                return True, "Target destroyed"
            else:
                drone.engaged = False
                self.log_event(f"MISS: Target ID:{drone_id} evaded")
                return False, "Target evaded"
        
        thread = threading.Thread(target=engagement_result, daemon=True)
        thread.start()
        
        return True, f"Missile launched at target ID:{drone_id}"
    
    def display_radar_screen(self):
        self.clear_screen()
        
        # Header
        self.print_colored("=" * 80, "cyan")
        self.print_colored("           ADVANCED DRONE DEFENSE SYSTEM v2.0", "bold")
        self.print_colored("=" * 80, "cyan")
        
        # System status
        status_color = "green" if self.system_status == "OPERATIONAL" else "red"
        threat_colors = {"GREEN": "green", "YELLOW": "yellow", "ORANGE": "yellow", "RED": "red"}
        
        print(f"System Status: ", end="")
        self.print_colored(self.system_status, status_color)
        print(f"Threat Level: ", end="")
        self.print_colored(self.threat_level, threat_colors.get(self.threat_level, "white"))
        print(f"Missiles Available: {self.missiles_available}")
        print(f"Radar Range: {self.radar_range}km | Auto-Engage: {'ON' if self.auto_engage else 'OFF'}")
        
        self.print_colored("-" * 80, "blue")
        
        # Tracked contacts
        tracked_drones = [d for d in self.drones.values() if d.tracked and not d.destroyed]
        
        if tracked_drones:
            self.print_colored("TRACKED CONTACTS:", "yellow")
            print(f"{'ID':<4} {'TYPE':<8} {'THREAT':<8} {'DIST':<8} {'ALT':<8} {'SPEED':<8} {'STATUS':<10}")
            print("-" * 70)
            
            for drone in sorted(tracked_drones, key=lambda d: d.id):
                distance = self.calculate_distance(self.base_x, self.base_y, drone.x, drone.y)
                
                status = "ENGAGED" if drone.engaged else "TRACKING"
                status_color = "red" if drone.engaged else "green"
                
                threat_color = {"LOW": "green", "MEDIUM": "yellow", 
                              "HIGH": "yellow", "CRITICAL": "red"}.get(drone.threat_level, "white")
                
                type_color = {"HOSTILE": "red", "MILITARY": "yellow", 
                            "CIVILIAN": "green", "UNKNOWN": "white"}.get(drone.type, "white")
                
                print(f"{drone.id:<4} ", end="")
                self.print_colored(f"{drone.type:<8}", type_color, end=" ")
                self.print_colored(f"{drone.threat_level:<8}", threat_color, end=" ")
                print(f"{distance:<7.1f}k {drone.altitude:<7.0f}m {drone.speed:<7.0f}k ", end="")
                self.print_colored(status, status_color)
        else:
            self.print_colored("No contacts tracked", "green")
        
        self.print_colored("-" * 80, "blue")
        
        # Recent events log
        self.print_colored("SYSTEM LOG:", "cyan")
        for event in self.engagement_log[-8:]:  # Show last 8 events
            if "HIT" in event:
                self.print_colored(event, "green")
            elif "MISS" in event or "RADAR" in event:
                self.print_colored(event, "yellow")
            elif "MISSILE" in event:
                self.print_colored(event, "red")
            else:
                print(event)
        
        self.print_colored("-" * 80, "blue")
        
        # Commands
        print("Commands: [E]ngage [T]oggle Auto [R]adar [S]tatus [Q]uit")
    
    def run_command_interface(self):
        while True:
            try:
                self.display_radar_screen()
                
                command = input("\nCommand: ").strip().upper()
                
                if command == 'Q':
                    self.print_colored("System shutting down...", "red")
                    break
                elif command == 'E':
                    try:
                        drone_id = int(input("Enter target ID: "))
                        success, message = self.engage_target(drone_id)
                        color = "green" if success else "red"
                        self.print_colored(message, color)
                        input("Press Enter to continue...")
                    except ValueError:
                        self.print_colored("Invalid target ID", "red")
                        input("Press Enter to continue...")
                elif command == 'T':
                    self.auto_engage = not self.auto_engage
                    status = "ENABLED" if self.auto_engage else "DISABLED"
                    self.print_colored(f"Auto-engage {status}", "yellow")
                    self.log_event(f"AUTO-ENGAGE: {status}")
                    input("Press Enter to continue...")
                elif command == 'R':
                    self.radar_active = not self.radar_active
                    status = "ACTIVE" if self.radar_active else "OFFLINE"
                    color = "green" if self.radar_active else "red"
                    self.print_colored(f"Radar {status}", color)
                    self.log_event(f"RADAR: {status}")
                    input("Press Enter to continue...")
                elif command == 'S':
                    self.show_detailed_status()
                    input("Press Enter to continue...")
                else:
                    time.sleep(0.1)  # Brief pause for invalid commands
                    
            except KeyboardInterrupt:
                self.print_colored("\nSystem shutting down...", "red")
                break
    
    def show_detailed_status(self):
        self.clear_screen()
        self.print_colored("DETAILED SYSTEM STATUS", "cyan")
        self.print_colored("=" * 50, "cyan")
        
        print(f"Total Drones in Area: {len(self.drones)}")
        print(f"Tracked Contacts: {sum(1 for d in self.drones.values() if d.tracked)}")
        print(f"Hostile Contacts: {sum(1 for d in self.drones.values() if d.type == 'HOSTILE')}")
        print(f"Engaged Targets: {sum(1 for d in self.drones.values() if d.engaged)}")
        print(f"Destroyed Targets: {sum(1 for d in self.drones.values() if d.destroyed)}")
        print(f"Missiles Fired: {24 - self.missiles_available}")
        print(f"System Uptime: {datetime.now().strftime('%H:%M:%S')}")
        
        self.print_colored("\nTHREAT BREAKDOWN:", "yellow")
        threat_counts = defaultdict(int)
        for drone in self.drones.values():
            if drone.tracked and not drone.destroyed:
                threat_counts[drone.threat_level] += 1
        
        for level, count in threat_counts.items():
            color = {"LOW": "green", "MEDIUM": "yellow", 
                    "HIGH": "yellow", "CRITICAL": "red"}.get(level, "white")
            self.print_colored(f"{level}: {count}", color)

def main():
    print("Initializing Drone Defense System...")
    time.sleep(2)
    
    defense_system = DefenseSystem()
    
    # Show startup sequence
    defense_system.clear_screen()
    defense_system.print_colored("DRONE DEFENSE SYSTEM INITIALIZATION", "cyan")
    defense_system.print_colored("=" * 50, "cyan")
    
    startup_messages = [
        "Loading radar systems...",
        "Initializing missile batteries...",
        "Connecting to satellite network...",
        "Calibrating targeting systems...",
        "Starting threat assessment...",
        "System ready for operation"
    ]
    
    for message in startup_messages:
        defense_system.print_colored(f"[OK] {message}", "green")
        time.sleep(1)
    
    defense_system.print_colored("\nPress Enter to begin operations...", "yellow")
    input()
    
    # Start main interface
    defense_system.run_command_interface()

if __name__ == "__main__":
    main()
