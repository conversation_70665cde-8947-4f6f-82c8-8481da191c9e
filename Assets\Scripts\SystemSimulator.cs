using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
using System.Linq;

[System.Serializable]
public class FileSystemItem
{
    public string Name;
    public bool IsDirectory;
    public string Content;
    public long Size;
    public DateTime LastModified;
    public string Permissions;
    public List<FileSystemItem> Children;
    
    public FileSystemItem(string name, bool isDirectory = false)
    {
        Name = name;
        IsDirectory = isDirectory;
        Content = "";
        Size = isDirectory ? 4096 : 0;
        LastModified = DateTime.Now;
        Permissions = isDirectory ? "drwxr-xr-x" : "-rw-r--r--";
        Children = new List<FileSystemItem>();
    }
}

[System.Serializable]
public class SystemProcess
{
    public int PID;
    public string Name;
    public string Status;
    public float CPUUsage;
    public float MemoryUsage;
    public DateTime StartTime;
    
    public SystemProcess(int pid, string name)
    {
        PID = pid;
        Name = name;
        Status = "Running";
        CPUUsage = UnityEngine.Random.Range(0.1f, 15.0f);
        MemoryUsage = UnityEngine.Random.Range(10f, 500f);
        StartTime = DateTime.Now.AddMinutes(-UnityEngine.Random.Range(1, 1440));
    }
}

public class SystemSimulator : MonoBehaviour
{
    [Header("System Configuration")]
    public float cpuUsage = 25.0f;
    public float memoryUsage = 60.0f;
    public float diskUsage = 45.0f;
    public float networkSpeed = 100.0f;
    
    [Header("File System")]
    public FileSystemItem rootDirectory;
    public FileSystemItem currentDirectory;
    
    [Header("Processes")]
    public List<SystemProcess> runningProcesses;
    
    [Header("Network")]
    public List<string> networkInterfaces;
    public Dictionary<string, string> networkConfig;
    
    private int nextPID = 1000;
    private Dictionary<string, string> environmentVariables;
    private List<string> systemServices;
    
    void Start()
    {
        InitializeFileSystem();
        InitializeProcesses();
        InitializeNetwork();
        InitializeEnvironment();
        StartCoroutine(UpdateSystemMetrics());
    }
    
    void InitializeFileSystem()
    {
        // Create root directory structure
        rootDirectory = new FileSystemItem("/", true);
        
        // Create standard Linux directories
        var home = new FileSystemItem("home", true);
        var user = new FileSystemItem("user", true);
        var etc = new FileSystemItem("etc", true);
        var var = new FileSystemItem("var", true);
        var usr = new FileSystemItem("usr", true);
        var bin = new FileSystemItem("bin", true);
        
        rootDirectory.Children.AddRange(new[] { home, etc, var, usr, bin });
        
        // Create user home directory structure
        var documents = new FileSystemItem("Documents", true);
        var downloads = new FileSystemItem("Downloads", true);
        var desktop = new FileSystemItem("Desktop", true);
        var pictures = new FileSystemItem("Pictures", true);
        
        user.Children.AddRange(new[] { documents, downloads, desktop, pictures });
        home.Children.Add(user);
        
        // Add some sample files
        var readme = new FileSystemItem("readme.txt", false);
        readme.Content = "Welcome to the System Simulator!\n\nThis is a realistic terminal simulation.\nYou can navigate the file system, run commands, and interact with the system.\n\nEnjoy exploring!";
        readme.Size = readme.Content.Length;
        
        var config = new FileSystemItem("config.conf", false);
        config.Content = "# System Configuration\nversion=1.0\ndebug=false\nmax_connections=100";
        config.Size = config.Content.Length;
        
        user.Children.AddRange(new[] { readme, config });
        
        // Set current directory to user home
        currentDirectory = user;
    }
    
    void InitializeProcesses()
    {
        runningProcesses = new List<SystemProcess>();
        
        // Add system processes
        runningProcesses.Add(new SystemProcess(1, "init"));
        runningProcesses.Add(new SystemProcess(2, "kthreadd"));
        runningProcesses.Add(new SystemProcess(100, "systemd"));
        runningProcesses.Add(new SystemProcess(150, "NetworkManager"));
        runningProcesses.Add(new SystemProcess(200, "sshd"));
        runningProcesses.Add(new SystemProcess(250, "apache2"));
        runningProcesses.Add(new SystemProcess(300, "mysql"));
        runningProcesses.Add(new SystemProcess(350, "unity"));
        runningProcesses.Add(new SystemProcess(400, "terminal"));
        
        nextPID = 500;
    }
    
    void InitializeNetwork()
    {
        networkInterfaces = new List<string> { "eth0", "lo", "wlan0" };
        networkConfig = new Dictionary<string, string>
        {
            {"eth0_ip", "*************"},
            {"eth0_netmask", "*************"},
            {"eth0_gateway", "***********"},
            {"lo_ip", "127.0.0.1"},
            {"wlan0_ip", "************"}
        };
    }
    
    void InitializeEnvironment()
    {
        environmentVariables = new Dictionary<string, string>
        {
            {"HOME", "/home/<USER>"},
            {"PATH", "/usr/local/bin:/usr/bin:/bin"},
            {"USER", "user"},
            {"SHELL", "/bin/bash"},
            {"TERM", "xterm-256color"},
            {"LANG", "en_US.UTF-8"}
        };
        
        systemServices = new List<string>
        {
            "apache2", "mysql", "ssh", "networking", "bluetooth", "cups"
        };
    }
    
    IEnumerator UpdateSystemMetrics()
    {
        while (true)
        {
            // Simulate realistic system metrics
            cpuUsage += UnityEngine.Random.Range(-5f, 5f);
            cpuUsage = Mathf.Clamp(cpuUsage, 5f, 95f);
            
            memoryUsage += UnityEngine.Random.Range(-3f, 3f);
            memoryUsage = Mathf.Clamp(memoryUsage, 20f, 90f);
            
            diskUsage += UnityEngine.Random.Range(-1f, 1f);
            diskUsage = Mathf.Clamp(diskUsage, 10f, 80f);
            
            networkSpeed += UnityEngine.Random.Range(-20f, 20f);
            networkSpeed = Mathf.Clamp(networkSpeed, 0f, 1000f);
            
            // Update process metrics
            foreach (var process in runningProcesses)
            {
                process.CPUUsage += UnityEngine.Random.Range(-2f, 2f);
                process.CPUUsage = Mathf.Clamp(process.CPUUsage, 0.1f, 25f);
                
                process.MemoryUsage += UnityEngine.Random.Range(-10f, 10f);
                process.MemoryUsage = Mathf.Clamp(process.MemoryUsage, 5f, 1000f);
            }
            
            yield return new WaitForSeconds(2f);
        }
    }
    
    public List<FileSystemItem> GetCurrentDirectoryContents()
    {
        return currentDirectory?.Children ?? new List<FileSystemItem>();
    }
    
    public string GetCurrentDirectory()
    {
        return GetFullPath(currentDirectory);
    }
    
    string GetFullPath(FileSystemItem item)
    {
        if (item == rootDirectory) return "/";
        
        List<string> pathParts = new List<string>();
        FileSystemItem current = item;
        
        while (current != null && current != rootDirectory)
        {
            pathParts.Insert(0, current.Name);
            current = FindParent(current);
        }
        
        return "/" + string.Join("/", pathParts);
    }
    
    FileSystemItem FindParent(FileSystemItem item)
    {
        return FindParentRecursive(rootDirectory, item);
    }
    
    FileSystemItem FindParentRecursive(FileSystemItem parent, FileSystemItem target)
    {
        if (parent.Children.Contains(target))
            return parent;
        
        foreach (var child in parent.Children)
        {
            if (child.IsDirectory)
            {
                var result = FindParentRecursive(child, target);
                if (result != null) return result;
            }
        }
        
        return null;
    }
    
    public bool ChangeDirectory(string path)
    {
        if (path == ".") return true;
        if (path == "..")
        {
            var parent = FindParent(currentDirectory);
            if (parent != null)
            {
                currentDirectory = parent;
                return true;
            }
            return false;
        }
        
        if (path.StartsWith("/"))
        {
            // Absolute path
            var target = FindItemByPath(path);
            if (target != null && target.IsDirectory)
            {
                currentDirectory = target;
                return true;
            }
        }
        else
        {
            // Relative path
            var target = currentDirectory.Children.FirstOrDefault(c => c.Name == path && c.IsDirectory);
            if (target != null)
            {
                currentDirectory = target;
                return true;
            }
        }
        
        return false;
    }
    
    FileSystemItem FindItemByPath(string path)
    {
        if (path == "/") return rootDirectory;
        
        string[] parts = path.Split('/').Where(p => !string.IsNullOrEmpty(p)).ToArray();
        FileSystemItem current = rootDirectory;
        
        foreach (string part in parts)
        {
            current = current.Children.FirstOrDefault(c => c.Name == part);
            if (current == null) return null;
        }
        
        return current;
    }
    
    public bool CreateDirectory(string name)
    {
        if (currentDirectory.Children.Any(c => c.Name == name))
            return false;
        
        var newDir = new FileSystemItem(name, true);
        currentDirectory.Children.Add(newDir);
        return true;
    }
    
    public void CreateFile(string name, string content)
    {
        var existingFile = currentDirectory.Children.FirstOrDefault(c => c.Name == name && !c.IsDirectory);
        if (existingFile != null)
        {
            existingFile.Content = content;
            existingFile.Size = content.Length;
            existingFile.LastModified = DateTime.Now;
        }
        else
        {
            var newFile = new FileSystemItem(name, false);
            newFile.Content = content;
            newFile.Size = content.Length;
            currentDirectory.Children.Add(newFile);
        }
    }
    
    public string GetFileContent(string fileName)
    {
        var file = currentDirectory.Children.FirstOrDefault(c => c.Name == fileName && !c.IsDirectory);
        return file?.Content;
    }
    
    public SystemProcess StartProcess(string processName)
    {
        var process = new SystemProcess(nextPID++, processName);
        runningProcesses.Add(process);
        return process;
    }
    
    public bool KillProcess(int pid)
    {
        var process = runningProcesses.FirstOrDefault(p => p.PID == pid);
        if (process != null)
        {
            runningProcesses.Remove(process);
            return true;
        }
        return false;
    }
    
    public List<SystemProcess> GetRunningProcesses()
    {
        return new List<SystemProcess>(runningProcesses);
    }
    
    public Dictionary<string, float> GetSystemMetrics()
    {
        return new Dictionary<string, float>
        {
            {"CPU", cpuUsage},
            {"Memory", memoryUsage},
            {"Disk", diskUsage},
            {"Network", networkSpeed}
        };
    }
    
    public Dictionary<string, string> GetNetworkConfiguration()
    {
        return new Dictionary<string, string>(networkConfig);
    }
    
    public List<string> GetNetworkInterfaces()
    {
        return new List<string>(networkInterfaces);
    }
    
    public string GetEnvironmentVariable(string name)
    {
        return environmentVariables.ContainsKey(name) ? environmentVariables[name] : null;
    }
    
    public void SetEnvironmentVariable(string name, string value)
    {
        environmentVariables[name] = value;
    }
    
    public List<string> GetSystemServices()
    {
        return new List<string>(systemServices);
    }
    
    public bool IsServiceRunning(string serviceName)
    {
        return systemServices.Contains(serviceName) && UnityEngine.Random.value > 0.2f; // 80% chance running
    }
    
    public void RestartService(string serviceName)
    {
        // Simulate service restart
        Debug.Log($"Restarting service: {serviceName}");
    }
    
    public void StopService(string serviceName)
    {
        // Simulate service stop
        Debug.Log($"Stopping service: {serviceName}");
    }
    
    public void StartService(string serviceName)
    {
        // Simulate service start
        Debug.Log($"Starting service: {serviceName}");
    }
}
