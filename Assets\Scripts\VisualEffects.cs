using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class VisualEffects : MonoBehaviour
{
    [Header("Terminal Effects")]
    public float typingSpeed = 0.05f;
    public AudioClip keyboardSound;
    public AudioClip bootupSound;
    public AudioClip errorSound;
    public AudioClip successSound;
    
    [Header("Window Effects")]
    public float windowFadeSpeed = 0.3f;
    public AnimationCurve windowScaleCurve;
    public float windowAnimationDuration = 0.2f;
    
    [Header("System Effects")]
    public ParticleSystem dataParticles;
    public Light screenGlow;
    public Image screenFlicker;
    
    [Header("Loading Effects")]
    public GameObject loadingSpinner;
    public TMP_Text loadingText;
    public string[] loadingMessages = {
        "Initializing system...",
        "Loading modules...",
        "Connecting to network...",
        "Starting services...",
        "System ready!"
    };
    
    private AudioSource audioSource;
    private Coroutine typingCoroutine;
    
    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
            audioSource = gameObject.AddComponent<AudioSource>();
        
        StartCoroutine(BootSequence());
    }
    
    IEnumerator BootSequence()
    {
        if (bootupSound != null)
            PlaySound(bootupSound);
        
        // Show loading screen
        if (loadingSpinner != null)
            loadingSpinner.SetActive(true);
        
        // Simulate boot process
        for (int i = 0; i < loadingMessages.Length; i++)
        {
            if (loadingText != null)
            {
                yield return StartCoroutine(TypeText(loadingText, loadingMessages[i]));
                yield return new WaitForSeconds(0.5f);
            }
        }
        
        // Hide loading screen
        if (loadingSpinner != null)
            loadingSpinner.SetActive(false);
        
        // Start ambient effects
        StartAmbientEffects();
    }
    
    void StartAmbientEffects()
    {
        // Start data particles
        if (dataParticles != null)
            dataParticles.Play();
        
        // Start screen glow animation
        if (screenGlow != null)
            StartCoroutine(AnimateScreenGlow());
        
        // Start subtle screen flicker
        if (screenFlicker != null)
            StartCoroutine(ScreenFlicker());
    }
    
    IEnumerator AnimateScreenGlow()
    {
        float baseIntensity = screenGlow.intensity;
        
        while (true)
        {
            float variation = Mathf.Sin(Time.time * 0.5f) * 0.1f;
            screenGlow.intensity = baseIntensity + variation;
            yield return null;
        }
    }
    
    IEnumerator ScreenFlicker()
    {
        while (true)
        {
            // Random flicker
            if (Random.value < 0.01f) // 1% chance per frame
            {
                screenFlicker.color = new Color(1, 1, 1, 0.05f);
                yield return new WaitForSeconds(0.05f);
                screenFlicker.color = Color.clear;
                yield return new WaitForSeconds(Random.Range(5f, 15f));
            }
            yield return null;
        }
    }
    
    public void AnimateWindowOpen(GameObject window)
    {
        if (window == null) return;
        
        StartCoroutine(WindowOpenAnimation(window));
    }
    
    IEnumerator WindowOpenAnimation(GameObject window)
    {
        CanvasGroup canvasGroup = window.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = window.AddComponent<CanvasGroup>();
        
        RectTransform rectTransform = window.GetComponent<RectTransform>();
        
        // Start from small scale and transparent
        Vector3 originalScale = rectTransform.localScale;
        rectTransform.localScale = Vector3.zero;
        canvasGroup.alpha = 0f;
        
        float elapsedTime = 0f;
        
        while (elapsedTime < windowAnimationDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / windowAnimationDuration;
            
            // Animate scale with curve
            float scaleProgress = windowScaleCurve.Evaluate(progress);
            rectTransform.localScale = Vector3.Lerp(Vector3.zero, originalScale, scaleProgress);
            
            // Animate alpha
            canvasGroup.alpha = Mathf.Lerp(0f, 1f, progress);
            
            yield return null;
        }
        
        // Ensure final values
        rectTransform.localScale = originalScale;
        canvasGroup.alpha = 1f;
    }
    
    public void AnimateWindowClose(GameObject window, System.Action onComplete = null)
    {
        if (window == null) return;
        
        StartCoroutine(WindowCloseAnimation(window, onComplete));
    }
    
    IEnumerator WindowCloseAnimation(GameObject window, System.Action onComplete)
    {
        CanvasGroup canvasGroup = window.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = window.AddComponent<CanvasGroup>();
        
        RectTransform rectTransform = window.GetComponent<RectTransform>();
        Vector3 originalScale = rectTransform.localScale;
        
        float elapsedTime = 0f;
        
        while (elapsedTime < windowAnimationDuration)
        {
            elapsedTime += Time.deltaTime;
            float progress = elapsedTime / windowAnimationDuration;
            
            // Animate scale
            rectTransform.localScale = Vector3.Lerp(originalScale, Vector3.zero, progress);
            
            // Animate alpha
            canvasGroup.alpha = Mathf.Lerp(1f, 0f, progress);
            
            yield return null;
        }
        
        onComplete?.Invoke();
    }
    
    public Coroutine TypeText(TMP_Text textComponent, string text)
    {
        if (typingCoroutine != null)
            StopCoroutine(typingCoroutine);
        
        typingCoroutine = StartCoroutine(TypeTextCoroutine(textComponent, text));
        return typingCoroutine;
    }
    
    IEnumerator TypeTextCoroutine(TMP_Text textComponent, string text)
    {
        if (textComponent == null) yield break;
        
        textComponent.text = "";
        
        for (int i = 0; i < text.Length; i++)
        {
            textComponent.text += text[i];
            
            // Play keyboard sound occasionally
            if (keyboardSound != null && Random.value < 0.3f)
            {
                PlaySound(keyboardSound, 0.1f);
            }
            
            yield return new WaitForSeconds(typingSpeed);
        }
    }
    
    public void PlaySound(AudioClip clip, float volume = 1f)
    {
        if (audioSource != null && clip != null)
        {
            audioSource.PlayOneShot(clip, volume);
        }
    }
    
    public void PlayErrorEffect()
    {
        if (errorSound != null)
            PlaySound(errorSound);
        
        StartCoroutine(ScreenFlash(Color.red));
    }
    
    public void PlaySuccessEffect()
    {
        if (successSound != null)
            PlaySound(successSound);
        
        StartCoroutine(ScreenFlash(Color.green));
    }
    
    IEnumerator ScreenFlash(Color color)
    {
        if (screenFlicker == null) yield break;
        
        screenFlicker.color = new Color(color.r, color.g, color.b, 0.2f);
        yield return new WaitForSeconds(0.1f);
        screenFlicker.color = Color.clear;
    }
    
    public void CreateMatrixEffect(Transform container)
    {
        StartCoroutine(MatrixRain(container));
    }
    
    IEnumerator MatrixRain(Transform container)
    {
        // Create falling text effect
        for (int i = 0; i < 20; i++)
        {
            GameObject textObj = new GameObject("MatrixChar");
            textObj.transform.SetParent(container);
            
            TMP_Text text = textObj.AddComponent<TextMeshProUGUI>();
            text.text = ((char)Random.Range(33, 126)).ToString();
            text.color = Color.green;
            text.fontSize = 12;
            
            RectTransform rectTransform = textObj.GetComponent<RectTransform>();
            rectTransform.anchoredPosition = new Vector2(
                Random.Range(-Screen.width / 2, Screen.width / 2),
                Screen.height / 2
            );
            
            StartCoroutine(AnimateMatrixChar(textObj));
            
            yield return new WaitForSeconds(0.1f);
        }
    }
    
    IEnumerator AnimateMatrixChar(GameObject charObj)
    {
        RectTransform rectTransform = charObj.GetComponent<RectTransform>();
        TMP_Text text = charObj.GetComponent<TMP_Text>();
        
        float speed = Random.Range(100f, 300f);
        
        while (rectTransform.anchoredPosition.y > -Screen.height / 2)
        {
            rectTransform.anchoredPosition += Vector2.down * speed * Time.deltaTime;
            
            // Fade out as it falls
            Color color = text.color;
            color.a = Mathf.Clamp01(rectTransform.anchoredPosition.y / (Screen.height / 2));
            text.color = color;
            
            // Randomly change character
            if (Random.value < 0.1f)
            {
                text.text = ((char)Random.Range(33, 126)).ToString();
            }
            
            yield return null;
        }
        
        Destroy(charObj);
    }
    
    public void CreateProgressBar(Transform parent, string label, System.Action<float> onProgress, System.Action onComplete)
    {
        StartCoroutine(AnimateProgressBar(parent, label, onProgress, onComplete));
    }
    
    IEnumerator AnimateProgressBar(Transform parent, string label, System.Action<float> onProgress, System.Action onComplete)
    {
        // Create progress bar UI
        GameObject progressBarObj = new GameObject("ProgressBar");
        progressBarObj.transform.SetParent(parent);
        
        // Add components and animate
        float progress = 0f;
        while (progress < 1f)
        {
            progress += Time.deltaTime * Random.Range(0.5f, 2f);
            progress = Mathf.Clamp01(progress);
            
            onProgress?.Invoke(progress);
            yield return null;
        }
        
        onComplete?.Invoke();
        Destroy(progressBarObj);
    }
    
    void Update()
    {
        // Handle special effects triggers
        if (Input.GetKeyDown(KeyCode.F12))
        {
            CreateMatrixEffect(transform);
        }
    }
}
