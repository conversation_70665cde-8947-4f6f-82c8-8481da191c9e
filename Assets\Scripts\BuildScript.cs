using UnityEngine;
using UnityEditor;
using System.IO;

public class BuildScript
{
    [MenuItem("Build/Build Windows")]
    public static void BuildWindows()
    {
        string buildPath = "Builds/Windows/SystemSimulator.exe";
        
        // Ensure build directory exists
        Directory.CreateDirectory(Path.GetDirectoryName(buildPath));
        
        BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions();
        buildPlayerOptions.scenes = new[] { "Assets/Scenes/MainScene.unity" };
        buildPlayerOptions.locationPathName = buildPath;
        buildPlayerOptions.target = BuildTarget.StandaloneWindows64;
        buildPlayerOptions.options = BuildOptions.None;
        
        BuildPipeline.BuildPlayer(buildPlayerOptions);
        
        Debug.Log("Build completed: " + buildPath);
    }
    
    [MenuItem("Build/Build Linux")]
    public static void BuildLinux()
    {
        string buildPath = "Builds/Linux/SystemSimulator";
        
        // Ensure build directory exists
        Directory.CreateDirectory(Path.GetDirectoryName(buildPath));
        
        BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions();
        buildPlayerOptions.scenes = new[] { "Assets/Scenes/MainScene.unity" };
        buildPlayerOptions.locationPathName = buildPath;
        buildPlayerOptions.target = BuildTarget.StandaloneLinux64;
        buildPlayerOptions.options = BuildOptions.None;
        
        BuildPipeline.BuildPlayer(buildPlayerOptions);
        
        Debug.Log("Build completed: " + buildPath);
    }
    
    [MenuItem("Build/Build Mac")]
    public static void BuildMac()
    {
        string buildPath = "Builds/Mac/SystemSimulator.app";
        
        // Ensure build directory exists
        Directory.CreateDirectory(Path.GetDirectoryName(buildPath));
        
        BuildPlayerOptions buildPlayerOptions = new BuildPlayerOptions();
        buildPlayerOptions.scenes = new[] { "Assets/Scenes/MainScene.unity" };
        buildPlayerOptions.locationPathName = buildPath;
        buildPlayerOptions.target = BuildTarget.StandaloneOSX;
        buildPlayerOptions.options = BuildOptions.None;
        
        BuildPipeline.BuildPlayer(buildPlayerOptions);
        
        Debug.Log("Build completed: " + buildPath);
    }
    
    [MenuItem("Build/Build All Platforms")]
    public static void BuildAllPlatforms()
    {
        BuildWindows();
        BuildLinux();
        BuildMac();
        
        Debug.Log("All platform builds completed!");
    }
}
