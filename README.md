# Realistic System Simulator

A comprehensive Unity-based system simulation that provides a realistic terminal interface, file system management, and system monitoring capabilities.

## Features

### 🖥️ Terminal System
- **Full Command Line Interface**: Complete terminal emulation with 30+ commands
- **Command History**: Navigate through previous commands with arrow keys
- **Auto-completion**: Tab completion for commands
- **Realistic Output**: Authentic system responses and error messages
- **Process Management**: View, start, and kill processes
- **Network Tools**: Ping, wget, netstat, ifconfig
- **File Operations**: ls, cat, mkdir, touch, chmod, and more

### 📁 File Manager
- **Visual File Browser**: Intuitive graphical file system navigation
- **File Operations**: Create, delete, copy, paste files and folders
- **Multiple View Modes**: List view with detailed information
- **Sorting Options**: Sort by name, size, or date
- **Address Bar**: Direct path navigation
- **Context Menus**: Right-click operations

### 📊 System Monitor
- **Real-time Metrics**: Live CPU, memory, disk, and network usage
- **Performance Graphs**: Visual representation of system performance
- **Process List**: Detailed process information with PID, CPU, and memory usage
- **System Information**: Hardware and OS details
- **Uptime Tracking**: System uptime display

### 🎨 Visual Effects
- **Smooth Animations**: Window opening/closing animations
- **Terminal Effects**: Typing animations and sound effects
- **Screen Effects**: Subtle screen glow and flicker
- **Matrix Rain**: Special effects (F12 key)
- **Boot Sequence**: Realistic system startup

### 🖱️ Desktop Environment
- **Desktop Icons**: Double-click to launch applications
- **Taskbar**: Application management and system information
- **Window Management**: Drag, minimize, maximize, close windows
- **Start Menu**: Application launcher
- **System Tray**: Clock and system stats

## Controls

### Keyboard Shortcuts
- **F1**: Open Terminal
- **F2**: Open File Manager  
- **F3**: Open System Monitor
- **Alt + Tab**: Cycle through open windows
- **F5**: Refresh (in File Manager and System Monitor)
- **F12**: Matrix effect
- **Arrow Keys**: Command history navigation (in Terminal)
- **Tab**: Auto-complete commands (in Terminal)

### Terminal Commands
```bash
# File System
ls / dir          # List directory contents
pwd              # Show current directory
cd <path>        # Change directory
mkdir <name>     # Create directory
touch <file>     # Create file
cat <file>       # Show file content
chmod <perm>     # Change permissions

# System Information
ps               # Show processes
top              # System information
kill <pid>       # Kill process
history          # Command history

# Network
ping <host>      # Ping network host
wget <url>       # Download file
netstat          # Network connections
ifconfig         # Network configuration

# System Control
systemctl <cmd>  # System service control
service <name>   # Service management
sudo <cmd>       # Execute as root

# Utilities
echo <text>      # Display text
clear            # Clear screen
help             # Show help
exit             # Close terminal
```

## Installation

### Prerequisites
- Unity 2021.3 LTS or later
- TextMeshPro package (automatically imported)

### Setup
1. Clone or download this repository
2. Open the project in Unity
3. Open the `MainScene` in `Assets/Scenes/`
4. Press Play to run the simulation

### Building
Use the build menu in Unity:
- **Build/Build Windows**: Create Windows executable
- **Build/Build Linux**: Create Linux executable  
- **Build/Build Mac**: Create macOS application
- **Build/Build All Platforms**: Build for all platforms

Or use the build script:
```csharp
// In Unity Editor
BuildScript.BuildWindows();
BuildScript.BuildAllPlatforms();
```

## Project Structure

```
Assets/
├── Scenes/
│   └── MainScene.unity          # Main scene file
├── Scripts/
│   ├── TerminalSystem.cs        # Terminal implementation
│   ├── SystemSimulator.cs       # Core system simulation
│   ├── UIManager.cs             # UI management
│   ├── FileManager.cs           # File browser
│   ├── SystemMonitor.cs         # Performance monitoring
│   ├── VisualEffects.cs         # Visual effects and animations
│   ├── WindowDragger.cs         # Window dragging functionality
│   ├── SceneSetup.cs            # Automatic scene setup
│   └── BuildScript.cs           # Build automation
└── README.md                    # This file
```

## Technical Details

### Architecture
- **Modular Design**: Each component is self-contained and reusable
- **Event-Driven**: Components communicate through Unity events
- **Realistic Simulation**: Authentic file system and process simulation
- **Performance Optimized**: Efficient rendering and memory usage

### File System Simulation
- **Virtual File System**: Complete directory structure simulation
- **File Operations**: Create, read, write, delete operations
- **Permissions**: Unix-style permission system
- **Metadata**: File sizes, dates, and attributes

### Process Simulation
- **Dynamic Processes**: Realistic process creation and management
- **Resource Usage**: Simulated CPU and memory consumption
- **Process States**: Running, stopped, zombie states
- **PID Management**: Unique process identifiers

## Customization

### Adding New Commands
```csharp
// In TerminalSystem.cs InitializeCommands()
commands.Add("mycommand", MyCommandFunction);

void MyCommandFunction(string[] args)
{
    AddOutput("My custom command output", normalTextColor);
}
```

### Modifying Visual Effects
```csharp
// In VisualEffects.cs
public void MyCustomEffect()
{
    StartCoroutine(MyEffectCoroutine());
}
```

### Adding New File Types
```csharp
// In FileManager.cs OpenFile()
if (file.Name.EndsWith(".myext"))
{
    // Handle custom file type
}
```

## Performance

### System Requirements
- **Minimum**: 2GB RAM, DirectX 11 compatible graphics
- **Recommended**: 4GB RAM, dedicated graphics card
- **Resolution**: 1280x720 minimum, 1920x1080 recommended

### Optimization Features
- **Object Pooling**: Efficient memory management
- **LOD System**: Level-of-detail for complex UI
- **Culling**: Off-screen element culling
- **Batching**: UI draw call optimization

## Troubleshooting

### Common Issues
1. **Terminal not responding**: Check if EventSystem is present in scene
2. **Missing UI elements**: Ensure TextMeshPro is imported
3. **Performance issues**: Reduce particle effects or disable animations
4. **Build errors**: Check Unity version compatibility

### Debug Mode
Enable debug logging by adding `DEBUG_MODE` to scripting define symbols in Player Settings.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

- **Developer**: System Simulator Team
- **Unity Version**: 2021.3 LTS
- **UI Framework**: Unity UI Toolkit
- **Text Rendering**: TextMeshPro

## Support

For support, please open an issue on the GitHub repository or contact the development team.

---

**Enjoy exploring the realistic system simulation!** 🚀
