#!/bin/bash

echo "Starting Unity Project - Realistic System Simulator"
echo "================================================"

# Check if Unity is installed
if ! command -v unity &> /dev/null; then
    echo "Unity not found. Please install Unity 2021.3 LTS or later."
    echo "You can download it from: https://unity3d.com/get-unity/download"
    exit 1
fi

# Check if project files exist
if [ ! -d "Assets" ]; then
    echo "Error: Assets folder not found. Make sure you're in the project directory."
    exit 1
fi

if [ ! -d "ProjectSettings" ]; then
    echo "Error: ProjectSettings folder not found. Make sure you're in the project directory."
    exit 1
fi

echo "Opening Unity project..."
echo ""
echo "Instructions:"
echo "1. Unity will open the project"
echo "2. Open the MainScene in Assets/Scenes/"
echo "3. Press the Play button to run the simulation"
echo "4. Use F1, F2, F3 to open Terminal, File Manager, and System Monitor"
echo ""
echo "Controls:"
echo "- F1: Terminal"
echo "- F2: File Manager"
echo "- F3: System Monitor"
echo "- F12: Matrix effect"
echo "- Alt+Tab: Cycle windows"
echo ""

# Make script executable
chmod +x "$0"

# Open Unity with this project
unity -projectPath "$(pwd)"

echo "Unity project opened!"
