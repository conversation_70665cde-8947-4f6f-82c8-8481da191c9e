using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Linq;

public class FileManager : MonoBehaviour
{
    [Header("UI References")]
    public TMP_Text currentPathText;
    public Transform fileListContainer;
    public GameObject fileItemPrefab;
    public Button backButton;
    public Button homeButton;
    public Button refreshButton;
    public TMP_InputField addressBar;
    
    [Header("File Operations")]
    public Button newFolderButton;
    public Button deleteButton;
    public Button copyButton;
    public Button pasteButton;
    
    [Header("View Options")]
    public Toggle showHiddenFiles;
    public Dropdown sortDropdown;
    
    private SystemSimulator systemSimulator;
    private List<GameObject> fileItems = new List<GameObject>();
    private FileSystemItem selectedItem;
    private FileSystemItem copiedItem;
    
    void Start()
    {
        systemSimulator = FindObjectOfType<SystemSimulator>();
        SetupEventListeners();
        RefreshFileList();
    }
    
    void SetupEventListeners()
    {
        if (backButton != null)
            backButton.onClick.AddListener(GoBack);
        
        if (homeButton != null)
            homeButton.onClick.AddListener(GoHome);
        
        if (refreshButton != null)
            refreshButton.onClick.AddListener(RefreshFileList);
        
        if (newFolderButton != null)
            newFolderButton.onClick.AddListener(CreateNewFolder);
        
        if (deleteButton != null)
            deleteButton.onClick.AddListener(DeleteSelected);
        
        if (copyButton != null)
            copyButton.onClick.AddListener(CopySelected);
        
        if (pasteButton != null)
            pasteButton.onClick.AddListener(PasteItem);
        
        if (addressBar != null)
            addressBar.onEndEdit.AddListener(NavigateToPath);
        
        if (showHiddenFiles != null)
            showHiddenFiles.onValueChanged.AddListener((value) => RefreshFileList());
        
        if (sortDropdown != null)
            sortDropdown.onValueChanged.AddListener((value) => RefreshFileList());
    }
    
    void RefreshFileList()
    {
        ClearFileList();
        
        if (systemSimulator == null) return;
        
        var files = systemSimulator.GetCurrentDirectoryContents();
        
        // Filter hidden files if needed
        if (showHiddenFiles != null && !showHiddenFiles.isOn)
        {
            files = files.Where(f => !f.Name.StartsWith(".")).ToList();
        }
        
        // Sort files
        if (sortDropdown != null)
        {
            switch (sortDropdown.value)
            {
                case 0: // Name
                    files = files.OrderBy(f => f.IsDirectory ? 0 : 1).ThenBy(f => f.Name).ToList();
                    break;
                case 1: // Size
                    files = files.OrderBy(f => f.IsDirectory ? 0 : 1).ThenBy(f => f.Size).ToList();
                    break;
                case 2: // Date
                    files = files.OrderBy(f => f.IsDirectory ? 0 : 1).ThenBy(f => f.LastModified).ToList();
                    break;
            }
        }
        
        // Create file items
        foreach (var file in files)
        {
            CreateFileItem(file);
        }
        
        // Update current path
        if (currentPathText != null)
        {
            currentPathText.text = systemSimulator.GetCurrentDirectory();
        }
        
        if (addressBar != null)
        {
            addressBar.text = systemSimulator.GetCurrentDirectory();
        }
    }
    
    void CreateFileItem(FileSystemItem file)
    {
        if (fileItemPrefab == null || fileListContainer == null) return;
        
        GameObject item = Instantiate(fileItemPrefab, fileListContainer);
        fileItems.Add(item);
        
        // Set up file item components
        var nameText = item.transform.Find("NameText")?.GetComponent<TMP_Text>();
        var sizeText = item.transform.Find("SizeText")?.GetComponent<TMP_Text>();
        var dateText = item.transform.Find("DateText")?.GetComponent<TMP_Text>();
        var iconImage = item.transform.Find("Icon")?.GetComponent<Image>();
        
        if (nameText != null)
            nameText.text = file.Name;
        
        if (sizeText != null)
            sizeText.text = file.IsDirectory ? "Folder" : FormatFileSize(file.Size);
        
        if (dateText != null)
            dateText.text = file.LastModified.ToString("MM/dd/yyyy HH:mm");
        
        if (iconImage != null)
        {
            // Set appropriate icon based on file type
            iconImage.color = file.IsDirectory ? Color.yellow : Color.white;
        }
        
        // Set up click handlers
        var button = item.GetComponent<Button>();
        if (button == null)
            button = item.AddComponent<Button>();
        
        button.onClick.AddListener(() => SelectItem(file, item));
        
        // Set up double-click for navigation
        var doubleClickHandler = item.GetComponent<DoubleClickHandler>();
        if (doubleClickHandler == null)
            doubleClickHandler = item.AddComponent<DoubleClickHandler>();
        
        doubleClickHandler.OnDoubleClick = () => {
            if (file.IsDirectory)
            {
                NavigateToDirectory(file.Name);
            }
            else
            {
                OpenFile(file);
            }
        };
    }
    
    void SelectItem(FileSystemItem file, GameObject itemUI)
    {
        // Deselect previous item
        foreach (var item in fileItems)
        {
            var image = item.GetComponent<Image>();
            if (image != null)
                image.color = Color.white;
        }
        
        // Select current item
        var selectedImage = itemUI.GetComponent<Image>();
        if (selectedImage != null)
            selectedImage.color = Color.blue;
        
        selectedItem = file;
        
        // Update button states
        UpdateButtonStates();
    }
    
    void UpdateButtonStates()
    {
        bool hasSelection = selectedItem != null;
        
        if (deleteButton != null)
            deleteButton.interactable = hasSelection;
        
        if (copyButton != null)
            copyButton.interactable = hasSelection;
        
        if (pasteButton != null)
            pasteButton.interactable = copiedItem != null;
    }
    
    void NavigateToDirectory(string directoryName)
    {
        if (systemSimulator != null)
        {
            if (systemSimulator.ChangeDirectory(directoryName))
            {
                RefreshFileList();
            }
        }
    }
    
    void NavigateToPath(string path)
    {
        if (systemSimulator != null && !string.IsNullOrEmpty(path))
        {
            // This would need more sophisticated path parsing
            // For now, just refresh if it's the current path
            RefreshFileList();
        }
    }
    
    void GoBack()
    {
        if (systemSimulator != null)
        {
            if (systemSimulator.ChangeDirectory(".."))
            {
                RefreshFileList();
            }
        }
    }
    
    void GoHome()
    {
        if (systemSimulator != null)
        {
            systemSimulator.ChangeDirectory("/home/<USER>");
            RefreshFileList();
        }
    }
    
    void CreateNewFolder()
    {
        string folderName = "New Folder";
        int counter = 1;
        
        // Find unique name
        while (systemSimulator.GetCurrentDirectoryContents().Any(f => f.Name == folderName))
        {
            folderName = $"New Folder ({counter})";
            counter++;
        }
        
        if (systemSimulator.CreateDirectory(folderName))
        {
            RefreshFileList();
        }
    }
    
    void DeleteSelected()
    {
        if (selectedItem != null)
        {
            // In a real implementation, you'd remove from the file system
            Debug.Log($"Deleting: {selectedItem.Name}");
            RefreshFileList();
        }
    }
    
    void CopySelected()
    {
        if (selectedItem != null)
        {
            copiedItem = selectedItem;
            UpdateButtonStates();
        }
    }
    
    void PasteItem()
    {
        if (copiedItem != null && systemSimulator != null)
        {
            string newName = copiedItem.Name;
            int counter = 1;
            
            // Find unique name
            while (systemSimulator.GetCurrentDirectoryContents().Any(f => f.Name == newName))
            {
                string extension = System.IO.Path.GetExtension(copiedItem.Name);
                string nameWithoutExt = System.IO.Path.GetFileNameWithoutExtension(copiedItem.Name);
                newName = $"{nameWithoutExt} - Copy ({counter}){extension}";
                counter++;
            }
            
            if (copiedItem.IsDirectory)
            {
                systemSimulator.CreateDirectory(newName);
            }
            else
            {
                systemSimulator.CreateFile(newName, copiedItem.Content);
            }
            
            RefreshFileList();
        }
    }
    
    void OpenFile(FileSystemItem file)
    {
        Debug.Log($"Opening file: {file.Name}");
        
        // Here you could implement file opening based on extension
        if (file.Name.EndsWith(".txt"))
        {
            // Open in text editor
            var uiManager = FindObjectOfType<UIManager>();
            if (uiManager != null)
            {
                uiManager.OpenTextEditor();
            }
        }
    }
    
    void ClearFileList()
    {
        foreach (var item in fileItems)
        {
            if (item != null)
                Destroy(item);
        }
        fileItems.Clear();
        selectedItem = null;
        UpdateButtonStates();
    }
    
    string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        
        return $"{len:0.##} {sizes[order]}";
    }
}
