@echo off
echo Starting Unity Project - Realistic System Simulator
echo ================================================

REM Check if Unity is installed
where unity >nul 2>nul
if %errorlevel% neq 0 (
    echo Unity not found in PATH. Please install Unity 2021.3 LTS or later.
    echo You can download it from: https://unity3d.com/get-unity/download
    pause
    exit /b 1
)

REM Check if project files exist
if not exist "Assets" (
    echo Error: Assets folder not found. Make sure you're in the project directory.
    pause
    exit /b 1
)

if not exist "ProjectSettings" (
    echo Error: ProjectSettings folder not found. Make sure you're in the project directory.
    pause
    exit /b 1
)

echo Opening Unity project...
echo.
echo Instructions:
echo 1. <PERSON> will open the project
echo 2. Open the MainScene in Assets/Scenes/
echo 3. Press the Play button to run the simulation
echo 4. Use F1, F2, F3 to open Terminal, File Manager, and System Monitor
echo.
echo Controls:
echo - F1: Terminal
echo - F2: File Manager  
echo - F3: System Monitor
echo - F12: Matrix effect
echo - Alt+Tab: Cycle windows
echo.

REM Open Unity with this project
unity -projectPath "%cd%"

echo Unity project opened!
pause
