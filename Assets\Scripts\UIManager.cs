using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class UIManager : MonoBehaviour
{
    [Header("Main UI References")]
    public Canvas mainCanvas;
    public GameObject desktopPanel;
    public GameObject taskbar;
    public GameObject startMenu;
    
    [Header("Window Management")]
    public GameObject terminalWindowPrefab;
    public GameObject fileManagerWindowPrefab;
    public GameObject systemMonitorWindowPrefab;
    public Transform windowContainer;
    
    [Header("Taskbar Elements")]
    public Button startButton;
    public Transform taskbarIconContainer;
    public TMP_Text clockText;
    public TMP_Text systemStatsText;
    
    [Header("Desktop Icons")]
    public Transform desktopIconContainer;
    public GameObject desktopIconPrefab;
    
    [Header("System References")]
    public TerminalSystem terminalSystem;
    public SystemSimulator systemSimulator;
    
    private List<GameObject> openWindows = new List<GameObject>();
    private bool startMenuOpen = false;
    
    void Start()
    {
        InitializeUI();
        SetupEventListeners();
        CreateDesktopIcons();
        StartCoroutine(UpdateClock());
        StartCoroutine(UpdateSystemStats());
    }
    
    void InitializeUI()
    {
        if (startMenu != null)
            startMenu.SetActive(false);
        
        // Initialize taskbar
        if (taskbar != null)
            taskbar.SetActive(true);
        
        // Initialize desktop
        if (desktopPanel != null)
            desktopPanel.SetActive(true);
    }
    
    void SetupEventListeners()
    {
        if (startButton != null)
        {
            startButton.onClick.AddListener(ToggleStartMenu);
        }
        
        // Close start menu when clicking elsewhere
        if (desktopPanel != null)
        {
            var button = desktopPanel.GetComponent<Button>();
            if (button == null)
                button = desktopPanel.AddComponent<Button>();
            
            button.onClick.AddListener(() => {
                if (startMenuOpen)
                    ToggleStartMenu();
            });
        }
    }
    
    void CreateDesktopIcons()
    {
        if (desktopIconContainer == null || desktopIconPrefab == null) return;
        
        // Create desktop icons
        CreateDesktopIcon("Terminal", "terminal_icon", OpenTerminal);
        CreateDesktopIcon("File Manager", "folder_icon", OpenFileManager);
        CreateDesktopIcon("System Monitor", "monitor_icon", OpenSystemMonitor);
        CreateDesktopIcon("Text Editor", "text_icon", OpenTextEditor);
        CreateDesktopIcon("Web Browser", "browser_icon", OpenWebBrowser);
        CreateDesktopIcon("Settings", "settings_icon", OpenSettings);
    }
    
    void CreateDesktopIcon(string name, string iconName, System.Action onDoubleClick)
    {
        GameObject icon = Instantiate(desktopIconPrefab, desktopIconContainer);
        
        // Set icon properties
        var iconText = icon.GetComponentInChildren<TMP_Text>();
        if (iconText != null)
            iconText.text = name;
        
        // Set up double-click functionality
        var doubleClickHandler = icon.GetComponent<DoubleClickHandler>();
        if (doubleClickHandler == null)
            doubleClickHandler = icon.AddComponent<DoubleClickHandler>();
        
        doubleClickHandler.OnDoubleClick = onDoubleClick;
        
        // Position icon (you might want to implement a grid layout)
        var rectTransform = icon.GetComponent<RectTransform>();
        if (rectTransform != null)
        {
            // Simple positioning - you can enhance this
            int iconCount = desktopIconContainer.childCount - 1;
            float x = 50 + (iconCount % 3) * 120;
            float y = -50 - (iconCount / 3) * 120;
            rectTransform.anchoredPosition = new Vector2(x, y);
        }
    }
    
    void ToggleStartMenu()
    {
        startMenuOpen = !startMenuOpen;
        if (startMenu != null)
            startMenu.SetActive(startMenuOpen);
    }
    
    public void OpenTerminal()
    {
        if (terminalWindowPrefab != null)
        {
            GameObject terminalWindow = Instantiate(terminalWindowPrefab, windowContainer);
            openWindows.Add(terminalWindow);
            
            // Set up terminal system reference
            var terminal = terminalWindow.GetComponentInChildren<TerminalSystem>();
            if (terminal != null && systemSimulator != null)
            {
                // Connect terminal to system simulator
                terminal.GetComponent<TerminalSystem>().enabled = true;
            }
            
            BringWindowToFront(terminalWindow);
            CreateTaskbarIcon(terminalWindow, "Terminal");
        }
    }
    
    public void OpenFileManager()
    {
        if (fileManagerWindowPrefab != null)
        {
            GameObject fileManagerWindow = Instantiate(fileManagerWindowPrefab, windowContainer);
            openWindows.Add(fileManagerWindow);
            BringWindowToFront(fileManagerWindow);
            CreateTaskbarIcon(fileManagerWindow, "File Manager");
        }
    }
    
    public void OpenSystemMonitor()
    {
        if (systemMonitorWindowPrefab != null)
        {
            GameObject systemMonitorWindow = Instantiate(systemMonitorWindowPrefab, windowContainer);
            openWindows.Add(systemMonitorWindow);
            BringWindowToFront(systemMonitorWindow);
            CreateTaskbarIcon(systemMonitorWindow, "System Monitor");
        }
    }
    
    public void OpenTextEditor()
    {
        Debug.Log("Opening Text Editor...");
        // Implement text editor window
    }
    
    public void OpenWebBrowser()
    {
        Debug.Log("Opening Web Browser...");
        // Implement web browser window
    }
    
    public void OpenSettings()
    {
        Debug.Log("Opening Settings...");
        // Implement settings window
    }
    
    void BringWindowToFront(GameObject window)
    {
        if (window != null)
        {
            window.transform.SetAsLastSibling();
        }
    }
    
    void CreateTaskbarIcon(GameObject window, string windowTitle)
    {
        if (taskbarIconContainer == null) return;
        
        // Create a simple taskbar icon
        GameObject taskbarIcon = new GameObject("TaskbarIcon_" + windowTitle);
        taskbarIcon.transform.SetParent(taskbarIconContainer);
        
        // Add button component
        Button iconButton = taskbarIcon.AddComponent<Button>();
        Image iconImage = taskbarIcon.AddComponent<Image>();
        iconImage.color = Color.gray;
        
        // Set size
        RectTransform rectTransform = taskbarIcon.GetComponent<RectTransform>();
        rectTransform.sizeDelta = new Vector2(40, 30);
        
        // Add click functionality
        iconButton.onClick.AddListener(() => {
            if (window != null)
            {
                window.SetActive(!window.activeInHierarchy);
                if (window.activeInHierarchy)
                    BringWindowToFront(window);
            }
        });
        
        // Store reference for cleanup
        var windowController = window.GetComponent<WindowController>();
        if (windowController == null)
            windowController = window.AddComponent<WindowController>();
        
        windowController.taskbarIcon = taskbarIcon;
    }
    
    public void CloseWindow(GameObject window)
    {
        if (openWindows.Contains(window))
        {
            openWindows.Remove(window);
            
            // Remove taskbar icon
            var windowController = window.GetComponent<WindowController>();
            if (windowController != null && windowController.taskbarIcon != null)
            {
                Destroy(windowController.taskbarIcon);
            }
            
            Destroy(window);
        }
    }
    
    IEnumerator UpdateClock()
    {
        while (true)
        {
            if (clockText != null)
            {
                clockText.text = System.DateTime.Now.ToString("HH:mm:ss");
            }
            yield return new WaitForSeconds(1f);
        }
    }
    
    IEnumerator UpdateSystemStats()
    {
        while (true)
        {
            if (systemStatsText != null && systemSimulator != null)
            {
                var metrics = systemSimulator.GetSystemMetrics();
                systemStatsText.text = $"CPU: {metrics["CPU"]:F0}% | RAM: {metrics["Memory"]:F0}%";
            }
            yield return new WaitForSeconds(2f);
        }
    }
    
    void Update()
    {
        // Handle keyboard shortcuts
        if (Input.GetKeyDown(KeyCode.F1))
        {
            OpenTerminal();
        }
        else if (Input.GetKeyDown(KeyCode.F2))
        {
            OpenFileManager();
        }
        else if (Input.GetKeyDown(KeyCode.F3))
        {
            OpenSystemMonitor();
        }
        else if (Input.GetKey(KeyCode.LeftAlt) && Input.GetKeyDown(KeyCode.Tab))
        {
            CycleWindows();
        }
    }
    
    void CycleWindows()
    {
        if (openWindows.Count > 1)
        {
            // Move first window to end
            var firstWindow = openWindows[0];
            openWindows.RemoveAt(0);
            openWindows.Add(firstWindow);
            
            // Bring new first window to front
            BringWindowToFront(openWindows[0]);
        }
    }
}

// Helper class for window management
public class WindowController : MonoBehaviour
{
    public GameObject taskbarIcon;
    public bool isMinimized = false;
    
    public void MinimizeWindow()
    {
        isMinimized = true;
        gameObject.SetActive(false);
    }
    
    public void RestoreWindow()
    {
        isMinimized = false;
        gameObject.SetActive(true);
        transform.SetAsLastSibling();
    }
    
    public void CloseWindow()
    {
        var uiManager = FindObjectOfType<UIManager>();
        if (uiManager != null)
        {
            uiManager.CloseWindow(gameObject);
        }
    }
}

// Helper class for double-click detection
public class DoubleClickHandler : MonoBehaviour, UnityEngine.EventSystems.IPointerClickHandler
{
    public System.Action OnDoubleClick;
    private float lastClickTime = 0f;
    private float doubleClickThreshold = 0.3f;
    
    public void OnPointerClick(UnityEngine.EventSystems.PointerEventData eventData)
    {
        float timeSinceLastClick = Time.time - lastClickTime;
        
        if (timeSinceLastClick <= doubleClickThreshold)
        {
            OnDoubleClick?.Invoke();
        }
        
        lastClickTime = Time.time;
    }
}
