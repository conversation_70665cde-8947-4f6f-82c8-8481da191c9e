#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUTONOMOUS COUNTER-DRONE DEFENSE SYSTEM - LIVE VERSION
Quantum Mechanics Based Turret System - PARTIZAN
Real-time autonomous drone detection and engagement
"""

import os
import sys
import time
import random
import threading
import math
from datetime import datetime

class QuantumDefenseLive:
    def __init__(self):
        self.drones = {}
        self.targets_engaged = 0
        self.targets_destroyed = 0
        self.shots_fired = 0
        self.ammunition = 500
        self.battery = 100.0
        self.temperature = 25.0
        self.turret_rotation = 0.0
        self.turret_elevation = 0.0
        self.current_target = None
        self.system_log = []
        self.running = True
        
    def print_colored(self, text, color="white", end="\n"):
        colors = {
            "red": "\033[91m", "green": "\033[92m", "yellow": "\033[93m",
            "blue": "\033[94m", "magenta": "\033[95m", "cyan": "\033[96m",
            "white": "\033[97m", "reset": "\033[0m", "bold": "\033[1m"
        }
        print(f"{colors.get(color, colors['white'])}{text}{colors['reset']}", end=end)
    
    def clear_screen(self):
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def log_event(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        self.system_log.append(f"[{timestamp}] {message}")
        if len(self.system_log) > 6:
            self.system_log.pop(0)
    
    def generate_drone(self):
        drone_id = len(self.drones) + 1
        angle = random.uniform(0, 360)
        distance = random.uniform(3000, 6000)
        
        x = distance * math.sin(math.radians(angle))
        y = distance * math.cos(math.radians(angle))
        
        drone = {
            'id': drone_id,
            'x': x, 'y': y,
            'altitude': random.uniform(50, 1500),
            'speed': random.uniform(10, 80),
            'heading': random.uniform(0, 360),
            'type': random.choice(['CIVILIAN', 'COMMERCIAL', 'MILITARY', 'HOSTILE']),
            'threat': random.choice(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
            'signature': random.uniform(0.1, 2.0),
            'detected': True,
            'engaged': False,
            'destroyed': False,
            'last_seen': datetime.now()
        }
        
        self.drones[drone_id] = drone
        self.log_event(f"QUANTUM RADAR: Contact detected - ID:{drone_id}")
        self.log_event(f"QUANTUM AI: Target classified - {drone['type']}")
        
        # Auto-engage hostile targets
        if drone['type'] in ['MILITARY', 'HOSTILE'] and not drone['engaged']:
            self.engage_target(drone_id)
    
    def engage_target(self, drone_id):
        if drone_id not in self.drones or self.drones[drone_id]['destroyed']:
            return
        
        drone = self.drones[drone_id]
        drone['engaged'] = True
        self.current_target = drone_id
        self.targets_engaged += 1
        
        self.log_event(f"TARGETING: Acquiring target ID:{drone_id}")
        
        # Simulate engagement sequence
        def engagement():
            time.sleep(0.5)  # Alignment time
            self.log_event(f"TURRET: Aligned on target ID:{drone_id}")
            
            time.sleep(0.5)  # Firing delay
            self.ammunition -= 1
            self.shots_fired += 1
            self.log_event(f"QUANTUM TURRET: Firing at target ID:{drone_id}")
            
            # Calculate hit probability
            hit_chance = random.uniform(0.3, 0.8)
            
            time.sleep(2)  # Projectile flight time
            
            if random.random() < hit_chance:
                drone['destroyed'] = True
                self.targets_destroyed += 1
                self.log_event(f"TARGET DESTROYED: ID:{drone_id} eliminated")
                self.print_colored(f"🎯 TARGET DESTROYED: Drone {drone_id}", "green")
            else:
                drone['engaged'] = False
                self.log_event(f"TARGET MISSED: ID:{drone_id} evaded")
                self.print_colored(f"❌ MISS: Drone {drone_id} evaded", "yellow")
            
            self.current_target = None
        
        threading.Thread(target=engagement, daemon=True).start()
    
    def update_drones(self):
        for drone_id, drone in list(self.drones.items()):
            if not drone['destroyed']:
                # Update position
                heading_rad = math.radians(drone['heading'])
                distance = drone['speed'] * 0.1
                
                drone['x'] += distance * math.sin(heading_rad)
                drone['y'] += distance * math.cos(heading_rad)
                drone['heading'] += random.uniform(-5, 5)
                drone['altitude'] += random.uniform(-10, 10)
                drone['altitude'] = max(10, min(3000, drone['altitude']))
                
                # Remove if too far
                dist_from_base = math.sqrt(drone['x']**2 + drone['y']**2)
                if dist_from_base > 8000:
                    del self.drones[drone_id]
            else:
                # Remove destroyed drones after some time
                if (datetime.now() - drone['last_seen']).seconds > 10:
                    del self.drones[drone_id]
    
    def update_systems(self):
        # Update battery
        consumption = 0.1 + (len(self.drones) * 0.02)
        self.battery = max(0, self.battery - consumption)
        
        # Update temperature
        heat = 25 + (len(self.drones) * 2) + random.uniform(-2, 2)
        self.temperature = heat
        
        # Update turret position (simulate tracking)
        if self.current_target and self.current_target in self.drones:
            drone = self.drones[self.current_target]
            target_bearing = math.degrees(math.atan2(drone['x'], drone['y']))
            target_elevation = math.degrees(math.atan2(drone['altitude'], 
                                          math.sqrt(drone['x']**2 + drone['y']**2)))
            
            self.turret_rotation = target_bearing
            self.turret_elevation = target_elevation
    
    def display_status(self):
        self.clear_screen()
        
        # Header
        self.print_colored("=" * 90, "cyan")
        self.print_colored("    AUTONOMOUS COUNTER-DRONE DEFENSE SYSTEM - PARTIZAN", "bold")
        self.print_colored("         QUANTUM MECHANICS TURRET SYSTEM v3.0", "cyan")
        self.print_colored("=" * 90, "cyan")
        
        # System status
        accuracy = (self.targets_destroyed / max(1, self.shots_fired)) * 100
        processor_load = len(self.drones) * 15
        
        print("┌─ QUANTUM SYSTEMS ─────────────────┬─ TURRET STATUS ──────────────────┬─ ENGAGEMENT ─────────┐")
        print(f"│ Quantum Radar:     ACTIVE      │ Rotation:    {self.turret_rotation:6.1f}°      │ Targets Engaged: {self.targets_engaged:4d} │")
        print(f"│ Quantum Camera:    ACTIVE      │ Elevation:   {self.turret_elevation:6.1f}°      │ Targets Destroyed:{self.targets_destroyed:4d} │")
        print(f"│ Quantum Processor: {processor_load:3.0f}%        │ Ammunition:  {self.ammunition:6d}      │ Accuracy Rate:   {accuracy:5.1f}%│")
        print(f"│ Quantum Battery:   {self.battery:5.1f}%       │ Temperature: {self.temperature:5.1f}°C      │ Shots Fired:     {self.shots_fired:4d} │")
        
        target_display = f"ID:{self.current_target}" if self.current_target else "NONE"
        print(f"│ Quantum Communication:ONLINE  │ Range:       5000m     │ Current Target:  {target_display:<4} │")
        print("└───────────────────────────────────┴──────────────────────────────────┴──────────────────────┘")
        
        # Threat level
        hostile_count = sum(1 for d in self.drones.values() 
                          if d['type'] in ['MILITARY', 'HOSTILE'] and not d['destroyed'])
        
        if hostile_count == 0:
            threat_level = "GREEN"
            threat_color = "green"
        elif hostile_count <= 1:
            threat_level = "YELLOW"
            threat_color = "yellow"
        else:
            threat_level = "RED"
            threat_color = "red"
        
        print(f"\nThreat Level: ", end="")
        self.print_colored(f"{threat_level}", threat_color, end="")
        print(f" | System Status: ", end="")
        self.print_colored("OPERATIONAL", "green", end="")
        print(f" | Auto-Engage: ", end="")
        self.print_colored("ENABLED", "green")
        
        # Tracked contacts
        active_drones = [d for d in self.drones.values() if not d['destroyed']]
        
        self.print_colored("\n┌─ TRACKED CONTACTS ─────────────────────────────────────────────────────────────────┐", "blue")
        
        if active_drones:
            print("│ ID │ TYPE       │ THREAT   │ DISTANCE │ ALTITUDE │ SPEED │ STATUS     │ SIGNATURE │")
            print("├────┼────────────┼──────────┼──────────┼──────────┼───────┼────────────┼───────────┤")
            
            for drone in sorted(active_drones, key=lambda d: d['id'])[:8]:
                distance = math.sqrt(drone['x']**2 + drone['y']**2)
                status = "🎯ENGAGED" if drone['engaged'] else "👁 TRACKING"
                status_color = "red" if drone['engaged'] else "green"
                
                type_colors = {"HOSTILE": "red", "MILITARY": "yellow", 
                             "COMMERCIAL": "cyan", "CIVILIAN": "green"}
                type_color = type_colors.get(drone['type'], "white")
                
                threat_colors = {"LOW": "green", "MEDIUM": "yellow", 
                               "HIGH": "yellow", "CRITICAL": "red"}
                threat_color = threat_colors.get(drone['threat'], "white")
                
                print(f"│{drone['id']:3d} │ ", end="")
                self.print_colored(f"{drone['type']:<10}", type_color, end="")
                print(" │ ", end="")
                self.print_colored(f"{drone['threat']:<8}", threat_color, end="")
                print(f" │ {distance:7.0f}m │ {drone['altitude']:7.0f}m │ {drone['speed']:4.0f}m/s │ ", end="")
                self.print_colored(f"{status:<10}", status_color, end="")
                print(f" │ {drone['signature']:7.2f}m² │")
        else:
            print("│                              NO CONTACTS TRACKED                              │")
        
        print("└────────────────────────────────────────────────────────────────────────────────┘")
        
        # System log
        self.print_colored("\n┌─ SYSTEM LOG ───────────────────────────────────────────────────────────────────────┐", "cyan")
        for event in self.system_log[-6:]:
            if "DESTROYED" in event:
                print("│ ", end="")
                self.print_colored(event[:78], "green", end="")
                print(" │")
            elif "MISSED" in event or "EVADED" in event:
                print("│ ", end="")
                self.print_colored(event[:78], "yellow", end="")
                print(" │")
            elif "FIRING" in event or "TARGETING" in event:
                print("│ ", end="")
                self.print_colored(event[:78], "red", end="")
                print(" │")
            else:
                print(f"│ {event[:78]:<78} │")
        
        # Fill empty log lines
        for _ in range(6 - len(self.system_log[-6:])):
            print("│" + " " * 78 + "│")
        
        print("└────────────────────────────────────────────────────────────────────────────────┘")
        
        self.print_colored("\n🎯 QUANTUM DEFENSE SYSTEM ACTIVE - Press Ctrl+C to stop", "yellow")
    
    def run(self):
        # Startup sequence
        self.clear_screen()
        self.print_colored("🚀 Initializing Quantum Defense System...", "cyan")
        time.sleep(1)
        
        startup_messages = [
            "🔬 Initializing quantum processors...",
            "📡 Activating quantum radar array...",
            "📹 Calibrating quantum camera systems...",
            "🎯 Loading targeting algorithms...",
            "⚡ Establishing quantum field coherence...",
            "🔋 Quantum battery systems online...",
            "🌐 Quantum communication established...",
            "🛡️  Defense grid operational"
        ]
        
        for msg in startup_messages:
            self.print_colored(f"[✓] {msg}", "green")
            time.sleep(0.8)
        
        self.print_colored("\n🎯 SYSTEM READY FOR AUTONOMOUS OPERATION", "bold")
        time.sleep(2)
        
        # Main loop
        last_drone_spawn = time.time()
        
        try:
            while self.running:
                # Spawn new drones periodically
                if time.time() - last_drone_spawn > random.uniform(8, 20):
                    self.generate_drone()
                    last_drone_spawn = time.time()
                
                # Update systems
                self.update_drones()
                self.update_systems()
                
                # Display status
                self.display_status()
                
                # Wait before next update
                time.sleep(2)
                
        except KeyboardInterrupt:
            self.print_colored("\n🔴 EMERGENCY SHUTDOWN INITIATED", "red")
            self.running = False

def main():
    defense_system = QuantumDefenseLive()
    defense_system.run()

if __name__ == "__main__":
    main()
